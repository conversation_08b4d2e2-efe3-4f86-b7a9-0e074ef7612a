"""
任务执行数据查询MCP服务器
提供多个数据检索工具，用于查询和分析任务执行情况

主要功能：
1. query_tasks_by_duration - 根据执行时长查询任务
2. query_tasks_by_creator - 根据创建人查询任务  
3. query_tasks_by_status - 根据任务状态查询任务
4. query_tasks_by_date_range - 根据日期范围查询任务
5. get_task_statistics - 获取任务执行统计信息
"""

import random
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Annotated
from enum import Enum

from pydantic import BaseModel, Field
from fastmcp import FastMCP

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# ==================== 数据模型定义 ====================

class TaskStatus(str, Enum):
    """任务状态枚举"""
    SUCCESS = "成功"
    FAILED = "失败" 
    RUNNING = "运行中"
    PENDING = "等待中"
    CANCELLED = "已取消"

class ExecutionStatus(str, Enum):
    """执行状态枚举"""
    COMPLETED = "已完成"
    IN_PROGRESS = "执行中"
    FAILED = "执行失败"
    QUEUED = "排队中"

class TaskRecord(BaseModel):
    """任务记录数据模型"""
    table_name: str = Field(..., description="表名")
    task_name: str = Field(..., description="任务名称")
    execution_status: ExecutionStatus = Field(..., description="执行状态")
    execution_plan: str = Field(..., description="执行计划描述")
    execution_time: datetime = Field(..., description="执行时间")
    duration_seconds: float = Field(..., description="执行时长（秒）")
    task_status: TaskStatus = Field(..., description="任务状态")
    task_id: str = Field(..., description="任务ID")
    creator: str = Field(..., description="创建人")
    created_time: datetime = Field(..., description="创建时间")

class TaskQueryResult(BaseModel):
    """任务查询结果"""
    total_count: int = Field(..., description="符合条件的任务总数")
    tasks: List[TaskRecord] = Field(..., description="任务记录列表")
    query_summary: str = Field(..., description="查询结果摘要")

class TaskStatistics(BaseModel):
    """任务统计信息"""
    total_tasks: int = Field(..., description="任务总数")
    success_count: int = Field(..., description="成功任务数")
    failed_count: int = Field(..., description="失败任务数")
    running_count: int = Field(..., description="运行中任务数")
    avg_duration: float = Field(..., description="平均执行时长（秒）")
    max_duration: float = Field(..., description="最长执行时长（秒）")
    min_duration: float = Field(..., description="最短执行时长（秒）")
    creators: List[str] = Field(..., description="所有创建人列表")

# ==================== 模拟数据生成器 ====================

class TaskDataGenerator:
    """任务数据生成器 - 模拟真实环境的接口数据"""
    
    def __init__(self):
        self.creators = ["张三", "李四", "王五", "赵六", "钱七", "孙八", "周九", "吴十"]
        self.table_names = ["user_data", "order_info", "product_catalog", "inventory", "sales_report", "customer_feedback"]
        self.task_types = ["数据同步", "报表生成", "数据清洗", "备份任务", "统计分析", "数据导入", "索引重建"]
        self.execution_plans = [
            "每日凌晨2点自动执行",
            "每周一上午9点执行", 
            "手动触发执行",
            "数据变更时自动触发",
            "每小时执行一次",
            "每月1号执行"
        ]
        
        # 生成模拟数据
        self.mock_data = self._generate_mock_data()
    
    def _generate_mock_data(self) -> List[TaskRecord]:
        """生成模拟的任务数据"""
        tasks = []
        base_time = datetime.now()
        
        for i in range(50):  # 生成50条模拟数据
            # 随机生成任务执行时间（最近30天内）
            days_ago = random.randint(0, 30)
            hours_ago = random.randint(0, 23)
            minutes_ago = random.randint(0, 59)
            execution_time = base_time - timedelta(days=days_ago, hours=hours_ago, minutes=minutes_ago)
            
            # 创建时间通常早于执行时间
            created_time = execution_time - timedelta(hours=random.randint(1, 72))
            
            # 随机生成执行时长
            duration = round(random.uniform(0.5, 300), 2)  # 0.5秒到5分钟
            
            # 根据时长影响成功率（时长越长，失败概率越高）
            if duration > 120:
                status_weights = [0.6, 0.3, 0.05, 0.03, 0.02]  # 更容易失败
            elif duration > 60:
                status_weights = [0.75, 0.15, 0.05, 0.03, 0.02]
            else:
                status_weights = [0.85, 0.08, 0.03, 0.02, 0.02]  # 更容易成功
            
            task_status = random.choices(list(TaskStatus), weights=status_weights)[0]
            
            # 根据任务状态确定执行状态
            if task_status == TaskStatus.SUCCESS:
                exec_status = ExecutionStatus.COMPLETED
            elif task_status == TaskStatus.FAILED:
                exec_status = ExecutionStatus.FAILED
            elif task_status == TaskStatus.RUNNING:
                exec_status = ExecutionStatus.IN_PROGRESS
            else:
                exec_status = random.choice(list(ExecutionStatus))
            
            task = TaskRecord(
                table_name=random.choice(self.table_names),
                task_name=f"{random.choice(self.task_types)}_{random.choice(self.table_names)}_{i+1:03d}",
                execution_status=exec_status,
                execution_plan=random.choice(self.execution_plans),
                execution_time=execution_time,
                duration_seconds=duration,
                task_status=task_status,
                task_id=f"TASK_{i+1:05d}",
                creator=random.choice(self.creators),
                created_time=created_time
            )
            tasks.append(task)
        
        return sorted(tasks, key=lambda x: x.execution_time, reverse=True)
    
    def get_all_tasks(self) -> List[TaskRecord]:
        """获取所有任务数据"""
        return self.mock_data.copy()

# ==================== MCP服务器定义 ====================

# 创建FastMCP实例
mcp = FastMCP(name="Task Query & Analytics Server")

# 初始化数据生成器
data_generator = TaskDataGenerator()

@mcp.tool
def query_tasks_by_duration(
    min_duration: Annotated[float, Field(
        description="最小执行时长（秒）。用于筛选执行时间超过指定秒数的任务。"
                   "例如：30表示查询执行时间超过30秒的任务。"
                   "支持小数，如1.5表示1.5秒。",
        ge=0,
        le=3600
    )],
    max_duration: Annotated[Optional[float], Field(
        description="最大执行时长（秒），可选参数。如果指定，将查询执行时长在min_duration和max_duration之间的任务。"
                   "如果不指定，则查询所有执行时长大于等于min_duration的任务。",
        ge=0,
        le=3600
    )] = None,
    days_back: Annotated[int, Field(
        description="查询最近多少天内的任务，默认为7天。"
                   "例如：1表示查询今天，7表示查询最近一周。",
        ge=1,
        le=365
    )] = 7
) -> TaskQueryResult:
    """🕐 根据执行时长查询任务 - 找出执行时间过长或异常的任务
    
    **核心功能**：
    根据任务执行时长筛选任务，帮助识别性能问题和异常任务。
    
    **🎯 典型使用场景**：
    • "今天执行时间超过30秒的任务有哪些？"
    • "最近一周执行时长在60-120秒之间的任务"
    • "查找执行时间异常长的任务进行性能优化"
    • "统计长时间运行任务的分布情况"
    
    **📊 查询逻辑**：
    • 按执行时长范围筛选任务
    • 限制查询时间范围（避免数据量过大）
    • 按执行时间倒序排列（最新的任务在前）
    • 提供详细的查询结果摘要
    
    **💡 使用提示**：
    • min_duration=30 可查询执行超过30秒的慢任务
    • 结合days_back参数控制查询范围
    • 返回结果包含任务的完整执行信息
    """
    try:
        all_tasks = data_generator.get_all_tasks()
        cutoff_date = datetime.now() - timedelta(days=days_back)
        
        # 筛选符合条件的任务
        filtered_tasks = []
        for task in all_tasks:
            # 时间范围筛选
            if task.execution_time < cutoff_date:
                continue
                
            # 执行时长筛选
            if task.duration_seconds < min_duration:
                continue
                
            if max_duration is not None and task.duration_seconds > max_duration:
                continue
                
            filtered_tasks.append(task)
        
        # 生成查询摘要
        if max_duration:
            duration_desc = f"{min_duration}-{max_duration}秒"
        else:
            duration_desc = f"超过{min_duration}秒"
            
        summary = f"查询最近{days_back}天内执行时长{duration_desc}的任务，共找到{len(filtered_tasks)}个任务"
        
        if filtered_tasks:
            avg_duration = sum(t.duration_seconds for t in filtered_tasks) / len(filtered_tasks)
            summary += f"，平均执行时长{avg_duration:.2f}秒"
        
        logger.info(f"执行时长查询完成: {summary}")
        
        return TaskQueryResult(
            total_count=len(filtered_tasks),
            tasks=filtered_tasks,
            query_summary=summary
        )
        
    except Exception as e:
        logger.error(f"执行时长查询失败: {e}")
        return TaskQueryResult(
            total_count=0,
            tasks=[],
            query_summary=f"查询失败: {str(e)}"
        )

@mcp.tool
def query_tasks_by_creator(
    creator_name: Annotated[str, Field(
        description="创建人姓名，用于查询指定人员创建的任务。"
                   "支持完全匹配，如：'张三'、'李四'等。"
                   "区分大小写，请确保姓名准确。",
        min_length=1,
        max_length=50
    )],
    task_status: Annotated[Optional[str], Field(
        description="任务状态筛选，可选参数。"
                   "支持的状态：'成功'、'失败'、'运行中'、'等待中'、'已取消'。"
                   "如果不指定，则返回该创建人的所有状态任务。",
        pattern="^(成功|失败|运行中|等待中|已取消)$"
    )] = None,
    days_back: Annotated[int, Field(
        description="查询最近多少天内的任务，默认为7天。"
                   "用于限制查询时间范围，避免返回过多历史数据。",
        ge=1,
        le=365
    )] = 7
) -> TaskQueryResult:
    """👤 根据创建人查询任务 - 查看特定人员的任务执行情况

    **核心功能**：
    查询指定创建人的任务记录，支持按任务状态进一步筛选。

    **🎯 典型使用场景**：
    • "最近一周张三创建的有报错的任务有哪些？"
    • "李四今天创建的所有任务执行情况"
    • "查看王五最近的任务成功率"
    • "统计某个开发人员的任务质量"

    **📊 查询逻辑**：
    • 精确匹配创建人姓名
    • 可选择性筛选任务状态
    • 按创建时间倒序排列
    • 提供创建人的任务统计摘要

    **💡 使用提示**：
    • 创建人姓名必须完全匹配
    • task_status='失败' 可专门查询失败任务
    • 结合时间范围控制查询数据量
    """
    try:
        all_tasks = data_generator.get_all_tasks()
        cutoff_date = datetime.now() - timedelta(days=days_back)

        # 筛选符合条件的任务
        filtered_tasks = []
        for task in all_tasks:
            # 时间范围筛选
            if task.created_time < cutoff_date:
                continue

            # 创建人筛选
            if task.creator != creator_name:
                continue

            # 任务状态筛选
            if task_status and task.task_status.value != task_status:
                continue

            filtered_tasks.append(task)

        # 按创建时间排序
        filtered_tasks.sort(key=lambda x: x.created_time, reverse=True)

        # 生成查询摘要
        status_desc = f"状态为'{task_status}'的" if task_status else "所有状态的"
        summary = f"查询最近{days_back}天内{creator_name}创建的{status_desc}任务，共找到{len(filtered_tasks)}个任务"

        if filtered_tasks:
            # 统计各状态任务数量
            status_counts = {}
            for task in filtered_tasks:
                status = task.task_status.value
                status_counts[status] = status_counts.get(status, 0) + 1

            status_summary = "，".join([f"{k}:{v}个" for k, v in status_counts.items()])
            summary += f"（{status_summary}）"

        logger.info(f"创建人查询完成: {summary}")

        return TaskQueryResult(
            total_count=len(filtered_tasks),
            tasks=filtered_tasks,
            query_summary=summary
        )

    except Exception as e:
        logger.error(f"创建人查询失败: {e}")
        return TaskQueryResult(
            total_count=0,
            tasks=[],
            query_summary=f"查询失败: {str(e)}"
        )

@mcp.tool
def query_tasks_by_status(
    task_status: Annotated[str, Field(
        description="要查询的任务状态。"
                   "支持的状态值：'成功'、'失败'、'运行中'、'等待中'、'已取消'。"
                   "用于筛选特定状态的任务进行分析。",
        pattern="^(成功|失败|运行中|等待中|已取消)$"
    )],
    table_name: Annotated[Optional[str], Field(
        description="可选的表名筛选条件。"
                   "如果指定，将只返回操作该表的任务。"
                   "常见表名：'user_data'、'order_info'、'product_catalog'等。",
        min_length=1,
        max_length=100
    )] = None,
    days_back: Annotated[int, Field(
        description="查询最近多少天内的任务，默认为7天。"
                   "用于控制查询的时间范围。",
        ge=1,
        le=365
    )] = 7
) -> TaskQueryResult:
    """📊 根据任务状态查询任务 - 分析特定状态的任务分布

    **核心功能**：
    查询指定状态的任务，支持按表名进一步筛选，用于状态分析和问题排查。

    **🎯 典型使用场景**：
    • "最近一周失败的任务有哪些？"
    • "今天还在运行中的任务列表"
    • "user_data表相关的失败任务分析"
    • "统计各状态任务的分布情况"

    **📊 查询逻辑**：
    • 按任务状态精确筛选
    • 可选择性按表名进一步过滤
    • 按执行时间倒序排列
    • 提供状态相关的统计信息

    **💡 使用提示**：
    • task_status='失败' 专门查询问题任务
    • 结合table_name定位特定表的问题
    • 适合用于系统健康状态监控
    """
    try:
        all_tasks = data_generator.get_all_tasks()
        cutoff_date = datetime.now() - timedelta(days=days_back)

        # 筛选符合条件的任务
        filtered_tasks = []
        for task in all_tasks:
            # 时间范围筛选
            if task.execution_time < cutoff_date:
                continue

            # 任务状态筛选
            if task.task_status.value != task_status:
                continue

            # 表名筛选
            if table_name and task.table_name != table_name:
                continue

            filtered_tasks.append(task)

        # 按执行时间排序
        filtered_tasks.sort(key=lambda x: x.execution_time, reverse=True)

        # 生成查询摘要
        table_desc = f"操作'{table_name}'表的" if table_name else ""
        summary = f"查询最近{days_back}天内{table_desc}状态为'{task_status}'的任务，共找到{len(filtered_tasks)}个任务"

        if filtered_tasks:
            # 统计相关信息
            creators = list(set(task.creator for task in filtered_tasks))
            tables = list(set(task.table_name for task in filtered_tasks))
            avg_duration = sum(t.duration_seconds for t in filtered_tasks) / len(filtered_tasks)

            summary += f"，涉及{len(creators)}个创建人，{len(tables)}个表，平均执行时长{avg_duration:.2f}秒"

        logger.info(f"任务状态查询完成: {summary}")

        return TaskQueryResult(
            total_count=len(filtered_tasks),
            tasks=filtered_tasks,
            query_summary=summary
        )

    except Exception as e:
        logger.error(f"任务状态查询失败: {e}")
        return TaskQueryResult(
            total_count=0,
            tasks=[],
            query_summary=f"查询失败: {str(e)}"
        )

@mcp.tool
def query_tasks_by_date_range(
    start_date: Annotated[str, Field(
        description="查询开始日期，格式为YYYY-MM-DD。"
                   "例如：'2024-01-01'表示从2024年1月1日开始查询。"
                   "日期格式必须严格遵循ISO 8601标准。",
        pattern="^\\d{4}-\\d{2}-\\d{2}$"
    )],
    end_date: Annotated[str, Field(
        description="查询结束日期，格式为YYYY-MM-DD。"
                   "例如：'2024-01-31'表示查询到2024年1月31日结束。"
                   "结束日期必须大于等于开始日期。",
        pattern="^\\d{4}-\\d{2}-\\d{2}$"
    )],
    creator_name: Annotated[Optional[str], Field(
        description="可选的创建人筛选条件。"
                   "如果指定，将只返回该创建人在指定日期范围内的任务。",
        min_length=1,
        max_length=50
    )] = None
) -> TaskQueryResult:
    """📅 根据日期范围查询任务 - 精确的时间段任务分析

    **核心功能**：
    在指定的日期范围内查询任务，支持按创建人进一步筛选，适合周期性分析。

    **🎯 典型使用场景**：
    • "查询2024年1月份的所有任务执行情况"
    • "统计上个月张三创建的任务数量"
    • "分析特定时间段的任务成功率趋势"
    • "生成月度/季度任务执行报告"

    **📊 查询逻辑**：
    • 按任务执行时间进行日期范围筛选
    • 支持精确到天的时间控制
    • 可选择性按创建人过滤
    • 提供时间段内的详细统计信息

    **💡 使用提示**：
    • 日期格式必须为YYYY-MM-DD
    • 适合生成定期报告和趋势分析
    • 可以查询历史任务的执行模式
    """
    try:
        # 解析日期
        from datetime import datetime
        start_dt = datetime.strptime(start_date, "%Y-%m-%d")
        end_dt = datetime.strptime(end_date, "%Y-%m-%d").replace(hour=23, minute=59, second=59)

        if start_dt > end_dt:
            return TaskQueryResult(
                total_count=0,
                tasks=[],
                query_summary="错误：开始日期不能晚于结束日期"
            )

        all_tasks = data_generator.get_all_tasks()

        # 筛选符合条件的任务
        filtered_tasks = []
        for task in all_tasks:
            # 日期范围筛选
            if not (start_dt <= task.execution_time <= end_dt):
                continue

            # 创建人筛选
            if creator_name and task.creator != creator_name:
                continue

            filtered_tasks.append(task)

        # 按执行时间排序
        filtered_tasks.sort(key=lambda x: x.execution_time, reverse=True)

        # 生成查询摘要
        creator_desc = f"{creator_name}创建的" if creator_name else ""
        date_range = f"{start_date}至{end_date}"
        summary = f"查询{date_range}期间{creator_desc}任务，共找到{len(filtered_tasks)}个任务"

        if filtered_tasks:
            # 统计信息
            status_counts = {}
            total_duration = 0
            for task in filtered_tasks:
                status = task.task_status.value
                status_counts[status] = status_counts.get(status, 0) + 1
                total_duration += task.duration_seconds

            avg_duration = total_duration / len(filtered_tasks)
            status_summary = "，".join([f"{k}:{v}个" for k, v in status_counts.items()])
            summary += f"（{status_summary}，平均执行时长{avg_duration:.2f}秒）"

        logger.info(f"日期范围查询完成: {summary}")

        return TaskQueryResult(
            total_count=len(filtered_tasks),
            tasks=filtered_tasks,
            query_summary=summary
        )

    except ValueError as e:
        logger.error(f"日期格式错误: {e}")
        return TaskQueryResult(
            total_count=0,
            tasks=[],
            query_summary=f"日期格式错误: {str(e)}"
        )
    except Exception as e:
        logger.error(f"日期范围查询失败: {e}")
        return TaskQueryResult(
            total_count=0,
            tasks=[],
            query_summary=f"查询失败: {str(e)}"
        )

@mcp.tool
def get_task_statistics(
    days_back: Annotated[int, Field(
        description="统计最近多少天内的任务数据，默认为7天。"
                   "用于控制统计的时间范围，获取最新的任务执行趋势。",
        ge=1,
        le=365
    )] = 7,
    group_by_creator: Annotated[bool, Field(
        description="是否按创建人分组统计，默认为False。"
                   "设为True时，将提供每个创建人的详细统计信息。"
    )] = False
) -> TaskStatistics:
    """📈 获取任务执行统计信息 - 全面的任务数据分析和趋势洞察

    **核心功能**：
    生成指定时间范围内的任务执行统计报告，包含成功率、执行时长分析等关键指标。

    **🎯 典型使用场景**：
    • "最近一周的任务执行情况统计"
    • "获取系统整体健康状态概览"
    • "分析各创建人的任务质量和效率"
    • "生成任务执行趋势报告"

    **📊 统计指标**：
    • 任务总数和各状态分布
    • 成功率和失败率分析
    • 执行时长统计（平均/最大/最小）
    • 活跃创建人列表
    • 可选的按创建人分组详细统计

    **💡 使用提示**：
    • days_back=1 获取今日统计
    • group_by_creator=True 获取人员维度分析
    • 适合用于系统监控和性能评估
    """
    try:
        all_tasks = data_generator.get_all_tasks()
        cutoff_date = datetime.now() - timedelta(days=days_back)

        # 筛选时间范围内的任务
        recent_tasks = [
            task for task in all_tasks
            if task.execution_time >= cutoff_date
        ]

        if not recent_tasks:
            return TaskStatistics(
                total_tasks=0,
                success_count=0,
                failed_count=0,
                running_count=0,
                avg_duration=0.0,
                max_duration=0.0,
                min_duration=0.0,
                creators=[]
            )

        # 统计各状态任务数量
        status_counts = {
            "success": 0,
            "failed": 0,
            "running": 0,
            "other": 0
        }

        durations = []
        creators = set()

        for task in recent_tasks:
            creators.add(task.creator)
            durations.append(task.duration_seconds)

            if task.task_status == TaskStatus.SUCCESS:
                status_counts["success"] += 1
            elif task.task_status == TaskStatus.FAILED:
                status_counts["failed"] += 1
            elif task.task_status == TaskStatus.RUNNING:
                status_counts["running"] += 1
            else:
                status_counts["other"] += 1

        # 计算统计指标
        avg_duration = sum(durations) / len(durations)
        max_duration = max(durations)
        min_duration = min(durations)

        statistics = TaskStatistics(
            total_tasks=len(recent_tasks),
            success_count=status_counts["success"],
            failed_count=status_counts["failed"],
            running_count=status_counts["running"],
            avg_duration=round(avg_duration, 2),
            max_duration=round(max_duration, 2),
            min_duration=round(min_duration, 2),
            creators=sorted(list(creators))
        )

        logger.info(f"任务统计完成: 最近{days_back}天共{len(recent_tasks)}个任务，"
                   f"成功率{status_counts['success']/len(recent_tasks)*100:.1f}%")

        return statistics

    except Exception as e:
        logger.error(f"任务统计失败: {e}")
        return TaskStatistics(
            total_tasks=0,
            success_count=0,
            failed_count=0,
            running_count=0,
            avg_duration=0.0,
            max_duration=0.0,
            min_duration=0.0,
            creators=[]
        )

# ==================== 服务器启动 ====================

if __name__ == "__main__":
    print("🚀 启动任务查询与分析MCP服务器...")
    print("=" * 80)
    print("📋 服务器信息:")
    print("   名称: Task Query & Analytics Server")
    print("   功能: 任务执行数据查询和统计分析")

    # 可以通过环境变量配置端口和主机
    import os
    port = int(os.getenv("MCP_PORT", 8006))
    host = os.getenv("MCP_HOST", "0.0.0.0")

    print(f"   服务地址: http://{host}:{port}")
    print(f"   MCP端点: http://{host}:{port}/mcp/")
    print(f"   传输协议: Streamable HTTP")
    print()
    print("🛠️  可用工具 (5个):")
    print("   1. query_tasks_by_duration    - 🕐 根据执行时长查询任务")
    print("   2. query_tasks_by_creator     - 👤 根据创建人查询任务")
    print("   3. query_tasks_by_status      - 📊 根据任务状态查询任务")
    print("   4. query_tasks_by_date_range  - 📅 根据日期范围查询任务")
    print("   5. get_task_statistics        - 📈 获取任务执行统计信息")
    print()
    print("📊 模拟数据:")
    print(f"   任务总数: {len(data_generator.get_all_tasks())}条")
    print(f"   创建人员: {', '.join(data_generator.creators)}")
    print(f"   涉及表: {', '.join(data_generator.table_names)}")
    print()
    print("💡 使用示例:")
    print("   • '今天执行时间超过30秒的任务有哪些？'")
    print("   • '最近一周张三创建的有报错的任务有哪些？'")
    print("   • '查询失败状态的任务分布情况'")
    print("   • '获取最近7天的任务执行统计'")
    print()
    print("⚠️  注意事项:")
    print("   • 当前使用模拟数据，实际环境需要连接真实API")
    print("   • 所有查询都支持时间范围限制")
    print("   • 使用 Ctrl+C 停止服务器")
    print("=" * 80)

    try:
        # 使用streamable-http transport
        mcp.run(
            transport="streamable-http",
            host=host,
            port=port,
            show_banner=True
        )
    except KeyboardInterrupt:
        print("\n正在关闭服务器...")
        print("服务器已停止")
    except Exception as e:
        print(f"服务器启动失败: {e}")
