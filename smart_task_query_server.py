"""
智能任务查询MCP服务器
提供单一的智能自然语言查询工具，支持所有任务查询场景

主要功能：
1. query_tasks - 智能自然语言任务查询工具，一个工具处理所有查询需求
"""

import random
import logging
import re
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Annotated
from enum import Enum

from pydantic import BaseModel, Field
from fastmcp import FastMCP

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# ==================== 数据模型定义 ====================

class TaskStatus(str, Enum):
    """任务状态枚举"""
    SUCCESS = "成功"
    FAILED = "失败" 
    RUNNING = "运行中"
    PENDING = "等待中"
    CANCELLED = "已取消"

class ExecutionStatus(str, Enum):
    """执行状态枚举"""
    COMPLETED = "已完成"
    IN_PROGRESS = "执行中"
    FAILED = "执行失败"
    QUEUED = "排队中"

class TaskRecord(BaseModel):
    """任务记录数据模型"""
    table_name: str = Field(..., description="表名")
    task_name: str = Field(..., description="任务名称")
    execution_status: ExecutionStatus = Field(..., description="执行状态")
    execution_plan: str = Field(..., description="执行计划描述")
    execution_time: datetime = Field(..., description="执行时间")
    duration_seconds: float = Field(..., description="执行时长（秒）")
    task_status: TaskStatus = Field(..., description="任务状态")
    task_id: str = Field(..., description="任务ID")
    creator: str = Field(..., description="创建人")
    created_time: datetime = Field(..., description="创建时间")

class TaskQueryResult(BaseModel):
    """任务查询结果"""
    total_count: int = Field(..., description="符合条件的任务总数")
    tasks: List[TaskRecord] = Field(..., description="任务记录列表")
    query_summary: str = Field(..., description="查询结果摘要")

# ==================== 模拟数据生成器 ====================

class TaskDataGenerator:
    """任务数据生成器 - 模拟真实环境的接口数据"""
    
    def __init__(self):
        self.creators = ["张三", "李四", "王五", "赵六", "钱七", "孙八", "周九", "吴十"]
        self.table_names = ["user_data", "order_info", "product_catalog", "inventory", "sales_report", "customer_feedback"]
        self.task_types = ["数据同步", "报表生成", "数据清洗", "备份任务", "统计分析", "数据导入", "索引重建"]
        self.execution_plans = [
            "每日凌晨2点自动执行",
            "每周一上午9点执行", 
            "手动触发执行",
            "数据变更时自动触发",
            "每小时执行一次",
            "每月1号执行"
        ]
        
        # 生成模拟数据
        self.mock_data = self._generate_mock_data()
    
    def _generate_mock_data(self) -> List[TaskRecord]:
        """生成模拟的任务数据"""
        tasks = []
        base_time = datetime.now()
        
        for i in range(50):  # 生成50条模拟数据
            # 随机生成任务执行时间（最近30天内）
            days_ago = random.randint(0, 30)
            hours_ago = random.randint(0, 23)
            minutes_ago = random.randint(0, 59)
            execution_time = base_time - timedelta(days=days_ago, hours=hours_ago, minutes=minutes_ago)
            
            # 创建时间通常早于执行时间
            created_time = execution_time - timedelta(hours=random.randint(1, 72))
            
            # 随机生成执行时长
            duration = round(random.uniform(0.5, 300), 2)  # 0.5秒到5分钟
            
            # 根据时长影响成功率（时长越长，失败概率越高）
            if duration > 120:
                status_weights = [0.6, 0.3, 0.05, 0.03, 0.02]  # 更容易失败
            elif duration > 60:
                status_weights = [0.75, 0.15, 0.05, 0.03, 0.02]
            else:
                status_weights = [0.85, 0.08, 0.03, 0.02, 0.02]  # 更容易成功
            
            task_status = random.choices(list(TaskStatus), weights=status_weights)[0]
            
            # 根据任务状态确定执行状态
            if task_status == TaskStatus.SUCCESS:
                exec_status = ExecutionStatus.COMPLETED
            elif task_status == TaskStatus.FAILED:
                exec_status = ExecutionStatus.FAILED
            elif task_status == TaskStatus.RUNNING:
                exec_status = ExecutionStatus.IN_PROGRESS
            else:
                exec_status = random.choice(list(ExecutionStatus))
            
            task = TaskRecord(
                table_name=random.choice(self.table_names),
                task_name=f"{random.choice(self.task_types)}_{random.choice(self.table_names)}_{i+1:03d}",
                execution_status=exec_status,
                execution_plan=random.choice(self.execution_plans),
                execution_time=execution_time,
                duration_seconds=duration,
                task_status=task_status,
                task_id=f"TASK_{i+1:05d}",
                creator=random.choice(self.creators),
                created_time=created_time
            )
            tasks.append(task)
        
        return sorted(tasks, key=lambda x: x.execution_time, reverse=True)
    
    def get_all_tasks(self) -> List[TaskRecord]:
        """获取所有任务数据"""
        return self.mock_data.copy()

# ==================== MCP服务器定义 ====================

# 创建FastMCP实例
mcp = FastMCP(name="Smart Task Query Server")

# 初始化数据生成器
data_generator = TaskDataGenerator()

# ==================== 自然语言解析辅助函数 ====================

def _parse_query(query_text: str) -> Dict[str, Any]:
    """解析自然语言查询，提取查询条件"""
    conditions = {
        'creators': [],
        'statuses': [],
        'tables': [],
        'min_duration': None,
        'max_duration': None,
        'days_back': 7,  # 默认查询最近7天
        'start_date': None,
        'end_date': None,
        'is_statistics': False
    }
    
    # 解析创建人
    creators = data_generator.creators
    for creator in creators:
        if creator in query_text:
            conditions['creators'].append(creator)
    
    # 解析任务状态
    status_mapping = {
        '成功': TaskStatus.SUCCESS,
        '失败': TaskStatus.FAILED,
        '报错': TaskStatus.FAILED,
        '错误': TaskStatus.FAILED,
        '运行中': TaskStatus.RUNNING,
        '等待中': TaskStatus.PENDING,
        '已取消': TaskStatus.CANCELLED
    }
    
    for keyword, status in status_mapping.items():
        if keyword in query_text:
            conditions['statuses'].append(status)
    
    # 解析表名
    for table in data_generator.table_names:
        if table in query_text:
            conditions['tables'].append(table)
    
    # 解析执行时长
    duration_patterns = [
        r'超过(\d+)秒',
        r'大于(\d+)秒',
        r'(\d+)秒以上',
        r'执行时间超过(\d+)',
        r'执行.*?(\d+).*?秒'
    ]
    
    for pattern in duration_patterns:
        match = re.search(pattern, query_text)
        if match:
            conditions['min_duration'] = float(match.group(1))
            break
    
    # 解析时间范围
    time_patterns = {
        '今天': 1,
        '昨天': 1,
        '最近一天': 1,
        '最近3天': 3,
        '最近一周': 7,
        '最近7天': 7,
        '最近两周': 14,
        '最近一个月': 30,
        '最近30天': 30
    }
    
    for keyword, days in time_patterns.items():
        if keyword in query_text:
            conditions['days_back'] = days
            break
    
    # 检查是否为统计查询
    stat_keywords = ['统计', '概览', '分析', '报告', '总数', '数量']
    if any(keyword in query_text for keyword in stat_keywords):
        conditions['is_statistics'] = True
    
    return conditions

def _filter_tasks(all_tasks: List[TaskRecord], conditions: Dict[str, Any]) -> List[TaskRecord]:
    """根据解析的条件筛选任务"""
    filtered_tasks = []
    cutoff_date = datetime.now() - timedelta(days=conditions['days_back'])

    for task in all_tasks:
        # 时间范围筛选
        if task.execution_time < cutoff_date:
            continue

        # 创建人筛选
        if conditions['creators'] and task.creator not in conditions['creators']:
            continue

        # 状态筛选
        if conditions['statuses'] and task.task_status not in conditions['statuses']:
            continue

        # 表名筛选
        if conditions['tables'] and task.table_name not in conditions['tables']:
            continue

        # 执行时长筛选
        if conditions['min_duration'] and task.duration_seconds < conditions['min_duration']:
            continue

        if conditions['max_duration'] and task.duration_seconds > conditions['max_duration']:
            continue

        filtered_tasks.append(task)

    # 按执行时间倒序排列
    filtered_tasks.sort(key=lambda x: x.execution_time, reverse=True)
    return filtered_tasks

def _generate_summary(query_text: str, filtered_tasks: List[TaskRecord], conditions: Dict[str, Any]) -> str:
    """生成查询结果摘要"""
    if conditions['is_statistics']:
        return _generate_statistics_summary(filtered_tasks, conditions)

    summary_parts = [f"根据查询'{query_text}'"]

    if conditions['creators']:
        summary_parts.append(f"创建人：{', '.join(conditions['creators'])}")

    if conditions['statuses']:
        status_names = [s.value for s in conditions['statuses']]
        summary_parts.append(f"状态：{', '.join(status_names)}")

    if conditions['tables']:
        summary_parts.append(f"表：{', '.join(conditions['tables'])}")

    if conditions['min_duration']:
        summary_parts.append(f"执行时长≥{conditions['min_duration']}秒")

    summary_parts.append(f"时间范围：最近{conditions['days_back']}天")
    summary_parts.append(f"找到{len(filtered_tasks)}个匹配任务")

    if filtered_tasks:
        # 添加统计信息
        status_counts = {}
        total_duration = 0
        for task in filtered_tasks:
            status = task.task_status.value
            status_counts[status] = status_counts.get(status, 0) + 1
            total_duration += task.duration_seconds

        avg_duration = total_duration / len(filtered_tasks)
        status_summary = "，".join([f"{k}:{v}个" for k, v in status_counts.items()])
        summary_parts.append(f"（{status_summary}，平均执行时长{avg_duration:.2f}秒）")

    return "，".join(summary_parts)

def _generate_statistics_summary(filtered_tasks: List[TaskRecord], conditions: Dict[str, Any]) -> str:
    """生成统计摘要"""
    if not filtered_tasks:
        return f"最近{conditions['days_back']}天内没有找到匹配的任务"

    # 统计各状态任务数量
    status_counts = {}
    total_duration = 0
    creators = set()
    tables = set()

    for task in filtered_tasks:
        status_counts[task.task_status.value] = status_counts.get(task.task_status.value, 0) + 1
        total_duration += task.duration_seconds
        creators.add(task.creator)
        tables.add(task.table_name)

    avg_duration = total_duration / len(filtered_tasks)
    success_rate = status_counts.get('成功', 0) / len(filtered_tasks) * 100

    summary = f"最近{conditions['days_back']}天任务统计：" \
              f"总计{len(filtered_tasks)}个任务，" \
              f"成功率{success_rate:.1f}%，" \
              f"平均执行时长{avg_duration:.2f}秒，" \
              f"涉及{len(creators)}个创建人，{len(tables)}个表。" \
              f"状态分布：{', '.join([f'{k}:{v}个' for k, v in status_counts.items()])}"

    return summary

# ==================== 主要工具定义 ====================

@mcp.tool
def query_tasks(
    query_text: Annotated[str, Field(
        description="自然语言查询描述，描述你想要查询的任务条件。"
                   "支持的查询类型包括："
                   "• 按执行时长：'今天执行时间超过30秒的任务'、'最近一周执行时长在60-120秒之间的任务'"
                   "• 按创建人：'张三创建的失败任务'、'李四最近3天的所有任务'"
                   "• 按状态：'失败的任务'、'运行中的任务'、'成功的任务'"
                   "• 按日期：'2024年1月的任务'、'上周的任务'、'今天的任务'"
                   "• 按表名：'user_data表相关的任务'、'order_info表的失败任务'"
                   "• 统计查询：'最近7天的任务统计'、'任务执行情况概览'"
                   "• 组合查询：'张三创建的超过30秒的失败任务'",
        min_length=5,
        max_length=200
    )],
    limit: Annotated[int, Field(
        description="返回结果的最大数量，默认为20。"
                   "用于控制返回数据量，避免结果过多。"
                   "设置为0表示返回所有匹配结果。",
        ge=0,
        le=100
    )] = 20
) -> TaskQueryResult:
    """🔍 智能任务查询工具 - 支持自然语言查询的万能任务检索器

    **核心功能**：
    通过自然语言描述查询任务，自动解析查询条件并返回匹配的任务记录。这是一个万能的查询工具，
    可以处理所有类型的任务查询需求，无需使用多个不同的工具。

    **🎯 支持的查询类型**：
    • **时长查询**：'今天执行超过30秒的任务'、'执行时间在60-120秒之间的任务'
    • **人员查询**：'张三创建的失败任务'、'李四最近3天的任务'
    • **状态查询**：'失败的任务'、'运行中的任务'、'成功状态的任务'
    • **时间查询**：'今天的任务'、'最近一周的任务'、'2024年1月的任务'
    • **表名查询**：'user_data表的任务'、'order_info表相关的失败任务'
    • **统计查询**：'任务执行统计'、'最近7天的概览'
    • **组合查询**：'张三创建的超过30秒的失败任务'

    **🤖 智能解析能力**：
    • 自动识别查询意图和条件
    • 支持中文自然语言表达
    • 智能提取时间、人员、状态等关键信息
    • 灵活处理各种查询组合
    • 支持模糊匹配和智能推理

    **📊 返回信息**：
    • 匹配的任务详细记录（表名、任务名、执行状态、执行计划、执行时间、执行时长、任务状态、任务ID、创建人、创建时间）
    • 查询结果统计摘要
    • 自动按相关性和时间排序

    **💡 使用示例**：
    • "今天执行时间超过30秒的任务有哪些？"
    • "最近一周张三创建的有报错的任务有哪些？"
    • "查询失败状态的任务"
    • "user_data表相关的任务执行情况"
    • "最近7天的任务统计概览"
    • "李四创建的运行中任务"

    **⚠️ 使用提示**：
    • 查询描述越具体，结果越精确
    • 支持中文日常语言表达
    • 可以组合多个查询条件
    • 统计类查询会返回汇总信息
    """
    try:
        # 解析自然语言查询
        query_conditions = _parse_query(query_text)

        # 获取所有任务数据
        all_tasks = data_generator.get_all_tasks()

        # 应用查询条件筛选任务
        filtered_tasks = _filter_tasks(all_tasks, query_conditions)

        # 应用结果限制
        if limit > 0:
            filtered_tasks = filtered_tasks[:limit]

        # 生成查询摘要
        summary = _generate_summary(query_text, filtered_tasks, query_conditions)

        logger.info(f"智能查询完成: {summary}")

        return TaskQueryResult(
            total_count=len(filtered_tasks),
            tasks=filtered_tasks,
            query_summary=summary
        )

    except Exception as e:
        logger.error(f"智能查询失败: {e}")
        return TaskQueryResult(
            total_count=0,
            tasks=[],
            query_summary=f"查询失败: {str(e)}"
        )

# ==================== 服务器启动 ====================

if __name__ == "__main__":
    print("🚀 启动智能任务查询MCP服务器...")
    print("=" * 80)
    print("📋 服务器信息:")
    print("   名称: Smart Task Query Server")
    print("   功能: 智能自然语言任务查询")

    # 可以通过环境变量配置端口和主机
    import os
    port = int(os.getenv("MCP_PORT", 8007))
    host = os.getenv("MCP_HOST", "0.0.0.0")

    print(f"   服务地址: http://{host}:{port}")
    print(f"   MCP端点: http://{host}:{port}/mcp/")
    print(f"   传输协议: Streamable HTTP")
    print()
    print("🛠️  可用工具 (1个万能工具):")
    print("   1. query_tasks - 🔍 智能自然语言任务查询工具")
    print()
    print("📊 模拟数据:")
    print(f"   任务总数: {len(data_generator.get_all_tasks())}条")
    print(f"   创建人员: {', '.join(data_generator.creators)}")
    print(f"   涉及表: {', '.join(data_generator.table_names)}")
    print()
    print("💡 支持的查询类型:")
    print("   • 时长查询: '今天执行时间超过30秒的任务'")
    print("   • 人员查询: '张三创建的失败任务'")
    print("   • 状态查询: '失败的任务'、'运行中的任务'")
    print("   • 时间查询: '最近一周的任务'")
    print("   • 表名查询: 'user_data表相关的任务'")
    print("   • 统计查询: '最近7天的任务统计'")
    print("   • 组合查询: '张三创建的超过30秒的失败任务'")
    print()
    print("⚠️  注意事项:")
    print("   • 一个工具处理所有查询需求，无需多个工具")
    print("   • 支持中文自然语言表达")
    print("   • 当前使用模拟数据，实际环境需要连接真实API")
    print("   • 使用 Ctrl+C 停止服务器")
    print("=" * 80)

    try:
        # 使用streamable-http transport
        mcp.run(
            transport="streamable-http",
            host=host,
            port=port,
            show_banner=True
        )
    except KeyboardInterrupt:
        print("\n正在关闭服务器...")
        print("服务器已停止")
    except Exception as e:
        print(f"服务器启动失败: {e}")
