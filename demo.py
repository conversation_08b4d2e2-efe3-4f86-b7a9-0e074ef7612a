# visual_data_analysis.py
from __future__ import annotations
import io
import hashlib
from pathlib import Path
from typing import Optional, Tuple

import pandas as pd
import streamlit as st

# Pygwalker 需 0.3.7+；如使用新 API：0.4.x 更佳
from pygwalker.api.streamlit import StreamlitRenderer

# ------------------------------
# 页面与样式
# ------------------------------
st.set_page_config(
    page_title="政务数据分析实验台",
    page_icon="📊",
    layout="wide"
)

# 顶部样式（政务蓝）
st.markdown("""
<style>
/* 顶部横幅 */
.app-banner {
    background: linear-gradient(90deg, #0B3A82 0%, #0A5BCF 100%);
    color: #fff; padding: 14px 20px; border-radius: 12px;
    display:flex; align-items:center; justify-content:space-between;
}
.app-title { font-size: 20px; font-weight: 600; letter-spacing: 0.5px; }
.app-badge { background:#ffffff22; padding:6px 10px; border-radius:999px; font-size:12px; }
.stat-card {
    border: 1px solid #EEF1F6; border-radius: 14px; padding: 16px;
    background: #fff; box-shadow: 0 1px 3px rgba(10,20,50,.05);
}
.stat-val { font-size: 20px; font-weight: 700; }
.stat-sub { color:#5f6b7a; font-size: 12px; }
</style>
<div class="app-banner">
  <div class="app-title">📊 政务数据分析实验台</div>
  <div class="app-badge">Pygwalker • 交互式可视分析</div>
</div>
""", unsafe_allow_html=True)

st.caption("适用于政务数据探索、指标对比、趋势洞察与报表配置沉淀。")

# ------------------------------
# 辅助函数
# ------------------------------
def _hash_bytes(b: bytes) -> str:
    return hashlib.sha256(b).hexdigest()[:12]

def _read_tabular(file: st.runtime.uploaded_file_manager.UploadedFile) -> Tuple[pd.DataFrame, str]:
    """尽量鲁棒地读取 CSV / Excel；返回 (df, source_name)"""
    name = file.name
    raw = file.read()
    ext = Path(name).suffix.lower()
    # 复位 BytesIO 供多次读取
    bio = io.BytesIO(raw)

    if ext in [".xls", ".xlsx"]:
        df = pd.read_excel(bio)
        return df, name
    # 默认按 CSV 读取，尝试常见中文编码
    for enc in ("utf-8-sig", "utf-8", "gbk", "gb2312"):
        try:
            bio.seek(0)
            df = pd.read_csv(bio, encoding=enc)
            return df, f"{name} (encoding={enc})"
        except Exception:
            continue
    # 最后尝试自动推断分隔符
    try:
        bio.seek(0)
        df = pd.read_csv(bio, sep=None, engine="python")
        return df, f"{name} (detected sep)"
    except Exception as e:
        raise RuntimeError(f"无法读取文件：{name}\n原因：{e}")

def _maybe_sample(df: pd.DataFrame, max_rows: int = 20000) -> Tuple[pd.DataFrame, Optional[str]]:
    """对超大数据做行级抽样，避免浏览器/内存爆炸"""
    if len(df) > max_rows:
        rate = max_rows / len(df)
        sdf = df.sample(n=max_rows, random_state=42).reset_index(drop=True)
        msg = f"数据行数 {len(df):,} 超过 {max_rows:,}，已随机抽样 {max_rows:,} 行（约 {rate:.1%}）。"
        return sdf, msg
    return df, None

# ------------------------------
# 侧边栏：上传与参数
# ------------------------------
with st.sidebar:
    st.header("📥 数据上传与设置", divider="rainbow")
    up = st.file_uploader(
        "上传 CSV 或 Excel（UTF-8/GBK 兼容）",
        type=["csv", "xlsx", "xls"],
        accept_multiple_files=False
    )

    enable_sampling = st.toggle("对超大数据自动抽样（建议开启）", value=True)
    max_rows = st.number_input("抽样上限行数", 5000, 200000, 50000, step=5000)

    st.subheader("🔧 渲染设置")
    spec_rw = st.toggle("启用图表配置读写（gw_config.json）", value=True,
                        help="开启后保存你的图表配置，便于复用与迭代。")
    st.caption("提示：首次渲染大型数据可能较慢；请尽量在本地开发环境运行。")

# ------------------------------
# 主体逻辑：数据装载 & Pygwalker
# ------------------------------
df: Optional[pd.DataFrame] = None
source_name = ""

if up is None:
    st.info("请在左侧上传数据文件。建议列包含：地区、年份、指标值（如 GDP、财政收入、人口、城镇化率、失业率等）。", icon="ℹ️")
else:
    try:
        df, source_name = _read_tabular(up)
        if enable_sampling:
            df, tip = _maybe_sample(df, max_rows=max_rows)
            if tip:
                st.warning(tip, icon="⚠️")
    except Exception as e:
        st.error(str(e))
        st.stop()

# 顶部数据卡片（若数据可用）
if df is not None:
    c1, c2, c3, c4 = st.columns(4)
    with c1:
        st.markdown('<div class="stat-card"><div class="stat-val">'
                    f'{len(df):,}</div><div class="stat-sub">记录数</div></div>', unsafe_allow_html=True)
    with c2:
        st.markdown('<div class="stat-card"><div class="stat-val">'
                    f'{df.shape[1]:,}</div><div class="stat-sub">字段数</div></div>', unsafe_allow_html=True)
    with c3:
        st.markdown('<div class="stat-card"><div class="stat-val">'
                    f'{df.select_dtypes(include="number").shape[1]:,}</div><div class="stat-sub">数值字段</div></div>', unsafe_allow_html=True)
    with c4:
        nunique_cols = (df.nunique(dropna=False) <= 20).sum()
        st.markdown('<div class="stat-card"><div class="stat-val">'
                    f'{nunique_cols:,}</div><div class="stat-sub">类目/枚举字段(≤20种)</div></div>', unsafe_allow_html=True)

    st.markdown("### 🧭 数据源")
    st.write(f"**文件**：{source_name}")
    st.dataframe(df.head(50), use_container_width=True)

# ------------------------------
# 构建并缓存 Pygwalker Renderer
# ------------------------------
@st.cache_resource(show_spinner=True)
def get_renderer(_bytes_hash: str, _spec_path: Optional[str]) -> StreamlitRenderer:
    """
    以文件内容哈希 + spec 路径作为 cache key，避免每次刷新都重建。
    当没有上传文件时，不构建 Renderer。
    """
    if "df_cache" not in st.session_state or st.session_state.df_cache is None:
        raise RuntimeError("没有可用的数据用于渲染。")
    renderer = StreamlitRenderer(
        st.session_state.df_cache,
        spec=_spec_path if _spec_path else None,
        spec_io_mode="rw" if _spec_path else "r"  # 没有路径就只读
    )
    return renderer

# 将 df 存入 session，便于缓存函数获取
if df is not None:
    st.session_state.df_cache = df.copy()
    bytes_hash = _hash_bytes(df.to_csv(index=False).encode("utf-8"))
    spec_path = "gw_config.json" if spec_rw else None

    st.markdown("### 📈 交互式可视分析（Pygwalker）")
    st.caption("提示：左键拖拽字段到行/列/数值通道，快速生成分布、对比与趋势图；右上角可保存配置。")

    try:
        renderer = get_renderer(bytes_hash, spec_path)
        # 新版 API：.explorer()
        renderer.explorer()   # 如果你在 0.3.7，可替换为 renderer.render()
    except Exception as e:
        st.error(f"Pygwalker 渲染异常：{e}")
        st.stop()

    # 导出工具
    with st.sidebar:
        st.subheader("📤 导出")
        st.download_button(
            "下载当前数据为 CSV",
            data=df.to_csv(index=False).encode("utf-8-sig"),
            file_name="export.csv",
            mime="text/csv"
        )
        st.caption("若需图表配置复用，请保留工作目录下的 gw_config.json。")

# ------------------------------
# 使用建议（政务分析场景）
# ------------------------------
with st.expander("💡 分析建议（政务主题）", expanded=False):
    st.markdown("""
- 将 **地区** 拖到「行」，**年份** 拖到「列」，将 **GDP(亿元)** 放入「数值」→ 制作地区年度 GDP 热力表，便于识别增长热点。
- 将 **财政收入(亿元)** 与 **GDP(亿元)** 同时放入数值通道，切换为**双轴图**，查看财政与经济增长的结构关系。
- 将 **城镇化率(%)** 放在颜色通道，与 **人口(万人)** 组合成散点图，识别城市化与人口规模的耦合效应。
- 使用 **过滤器**（右侧区域）限定年份区间（如 2019–2024），观察疫情前后变化。
- 将 **失业率(%)** 放入数值通道，对不同行业/地区分面（Facet），查看结构性差异。
""")