# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a FastMCP-based PostgreSQL log analysis server that provides 4 core MCP tools for analyzing PostgreSQL error logs and automatically fixing table schema issues.

### Core Architecture

The application is structured around several key components:

1. **PostgreSQLLogAnalyzer**: Parses PostgreSQL logs to identify error types (varchar length, char length, numeric precision, etc.) and extract table names, SQL statements, and insert data
2. **DatabaseManager**: Manages asyncpg connection pools and handles database operations like schema queries and table modifications
3. **FieldDiagnosisEngine**: Analyzes table schema against insert data to identify specific field problems and suggest fixes
4. **FastMCP Tools**: 4 exposed MCP tools that orchestrate the above components

### Database Configuration

The application uses hardcoded database configuration in `DATABASE_CONFIG` (lines 247-256):
- Host: 127.0.0.1:5432
- User: usr_ai
- Password: 123456
- Database: postgres

Connection pooling is handled via asyncpg with lazy initialization on first tool call.

## Development Commands

### Running the Server
```bash
python postgresql_mcp_server.py
```

The server runs on port 8005 by default (configurable via `MCP_PORT` env var) using streamable-http transport.

### Dependencies
Install dependencies using uv:
```bash
uv sync
```

Key dependencies:
- fastmcp: MCP server framework
- fastapi[standard]: Web framework (used by fastmcp)
- asyncpg: Async PostgreSQL driver
- psycopg2-binary: PostgreSQL adapter
- pydantic: Data validation

## MCP Tools Available

1. **analyze_postgresql_log**: Parses PostgreSQL logs to identify error types and extract metadata
2. **get_table_schema**: Retrieves complete table schema information from database
3. **diagnose_field_problems**: Analyzes table schema vs insert data to identify specific field issues
4. **modify_table_schema**: Generates and optionally executes ALTER TABLE statements (supports dry-run mode)

## Error Pattern Recognition

The log analyzer recognizes these PostgreSQL error patterns:
- `VARCHAR_LENGTH`: "value too long for type character varying(n)"
- `CHAR_LENGTH`: "value too long for type character(n)"  
- `TEXT_LENGTH`: "value too long for type text"
- `NUMERIC_PRECISION`: "numeric field overflow"

## Code Conventions

- Uses Python 3.12+ with type hints throughout
- Pydantic models for all data structures and validation
- AsyncIO for database operations with connection pooling
- Comprehensive logging with structured messages
- Error handling with try/catch blocks that return error states rather than raising exceptions