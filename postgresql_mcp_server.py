"""
PostgreSQL日志分析和表结构修改MCP Server
基于fastmcp开发，提供4个核心工具用于分析PostgreSQL错误日志并自动修复表结构问题
"""

import re
import json
import asyncio
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

import asyncpg
from pydantic import BaseModel, Field
from fastmcp import FastMCP

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# ==================== 数据模型定义 ====================

class ErrorType(str, Enum):
    """PostgreSQL错误类型枚举"""
    VARCHAR_LENGTH = "varchar_length"
    CHAR_LENGTH = "char_length" 
    TEXT_LENGTH = "text_length"
    NUMERIC_PRECISION = "numeric_precision"
    CONSTRAINT_VIOLATION = "constraint_violation"
    UNKNOWN = "unknown"

class LogAnalysisInput(BaseModel):
    """日志分析输入"""
    log_content: str = Field(..., description="PostgreSQL日志内容")
    log_type: str = Field(default="error", description="日志类型（error, warning, info等）")

class LogAnalysisOutput(BaseModel):
    """日志分析输出"""
    error_type: ErrorType = Field(..., description="识别的错误类型")
    table_name: Optional[str] = Field(None, description="涉及的表名")
    insert_data: Optional[Dict[str, Any]] = Field(None, description="尝试插入的数据")
    error_message: str = Field(..., description="错误信息")
    confidence: float = Field(..., description="识别置信度 (0-1)")
    original_sql: Optional[str] = Field(None, description="原始SQL语句")

class TableSchemaInput(BaseModel):
    """表结构查询输入"""
    table_name: str = Field(..., description="表名")
    schema_name: str = Field(default="public", description="schema名称")
    database_config: Dict[str, Any] = Field(..., description="数据库连接配置")

class FieldInfo(BaseModel):
    """字段信息"""
    column_name: str = Field(..., description="字段名")
    data_type: str = Field(..., description="数据类型")
    character_maximum_length: Optional[int] = Field(None, description="字符最大长度")
    numeric_precision: Optional[int] = Field(None, description="数值精度")
    numeric_scale: Optional[int] = Field(None, description="数值标度")
    is_nullable: bool = Field(..., description="是否可为空")
    column_default: Optional[str] = Field(None, description="默认值")
    ordinal_position: int = Field(..., description="字段位置")

class TableSchemaOutput(BaseModel):
    """表结构查询输出"""
    table_name: str = Field(..., description="表名")
    schema_name: str = Field(..., description="schema名称")
    fields: List[FieldInfo] = Field(..., description="字段信息列表")
    total_fields: int = Field(..., description="字段总数")
    table_exists: bool = Field(..., description="表是否存在")

class FieldDiagnosisInput(BaseModel):
    """字段问题诊断输入"""
    table_schema: TableSchemaOutput = Field(..., description="表结构信息")
    insert_data: Dict[str, Any] = Field(..., description="插入数据")
    error_type: ErrorType = Field(..., description="错误类型")

class FieldProblem(BaseModel):
    """字段问题信息"""
    field_name: str = Field(..., description="问题字段名")
    current_length: Optional[int] = Field(None, description="当前字段长度限制")
    required_length: int = Field(..., description="需要的最小长度")
    data_value: Any = Field(..., description="导致问题的数据值")
    suggested_length: int = Field(..., description="建议的新长度")

class FieldDiagnosisOutput(BaseModel):
    """字段问题诊断输出"""
    problem_fields: List[FieldProblem] = Field(..., description="问题字段列表")
    diagnosis_summary: str = Field(..., description="诊断摘要")
    modification_needed: bool = Field(..., description="是否需要修改表结构")

class ModificationOperation(BaseModel):
    """修改操作"""
    field_name: str = Field(..., description="字段名")
    operation_type: str = Field(..., description="操作类型（ALTER_LENGTH, ALTER_TYPE等）")
    new_definition: str = Field(..., description="新的字段定义")

class TableModificationInput(BaseModel):
    """表结构修改输入"""
    table_name: str = Field(..., description="表名")
    schema_name: str = Field(default="public", description="schema名称")
    modifications: List[ModificationOperation] = Field(..., description="修改操作列表")
    database_config: Dict[str, Any] = Field(..., description="数据库连接配置")
    dry_run: bool = Field(default=True, description="是否为预览模式")

class TableModificationOutput(BaseModel):
    """表结构修改输出"""
    sql_statements: List[str] = Field(..., description="SQL语句列表")
    execution_status: str = Field(..., description="执行状态")
    affected_rows: int = Field(default=0, description="影响的行数")
    success: bool = Field(..., description="是否成功")
    error_message: Optional[str] = Field(None, description="错误信息")
    is_dry_run: bool = Field(..., description="是否为预览模式")

# ==================== PostgreSQL日志分析器 ====================

class PostgreSQLLogAnalyzer:
    """PostgreSQL日志分析器"""
    
    # 常见错误模式
    ERROR_PATTERNS = {
        ErrorType.VARCHAR_LENGTH: [
            r"value too long for type character varying\((\d+)\)",
            r"value too long for type varchar\((\d+)\)",
        ],
        ErrorType.CHAR_LENGTH: [
            r"value too long for type character\((\d+)\)",
            r"value too long for type char\((\d+)\)",
        ],
        ErrorType.TEXT_LENGTH: [
            r"value too long for type text",
        ],
        ErrorType.NUMERIC_PRECISION: [
            r"numeric field overflow",
            r"value out of range for type",
        ]
    }
    
    # SQL语句提取模式
    SQL_PATTERNS = [
        r"INSERT INTO\s+([^\s\(]+)",
        r"UPDATE\s+([^\s]+)",
        r"STATEMENT:\s*(INSERT INTO.*?)(?:\n|$)",
        r"QUERY:\s*(INSERT INTO.*?)(?:\n|$)",
    ]
    
    def analyze_log(self, log_content: str) -> LogAnalysisOutput:
        """分析PostgreSQL日志"""
        error_type = self._identify_error_type(log_content)
        table_name = self._extract_table_name(log_content)
        insert_data = self._extract_insert_data(log_content)
        error_message = self._extract_error_message(log_content)
        original_sql = self._extract_sql_statement(log_content)
        confidence = self._calculate_confidence(error_type, table_name, error_message)
        
        return LogAnalysisOutput(
            error_type=error_type,
            table_name=table_name,
            insert_data=insert_data,
            error_message=error_message,
            confidence=confidence,
            original_sql=original_sql
        )
    
    def _identify_error_type(self, log_content: str) -> ErrorType:
        """识别错误类型"""
        for error_type, patterns in self.ERROR_PATTERNS.items():
            for pattern in patterns:
                if re.search(pattern, log_content, re.IGNORECASE):
                    return error_type
        return ErrorType.UNKNOWN
    
    def _extract_table_name(self, log_content: str) -> Optional[str]:
        """提取表名"""
        for pattern in self.SQL_PATTERNS:
            match = re.search(pattern, log_content, re.IGNORECASE | re.MULTILINE)
            if match:
                if "INSERT INTO" in pattern:
                    return match.group(1).strip('"').strip("'")
                else:
                    return match.group(1).strip('"').strip("'")
        return None
    
    def _extract_insert_data(self, log_content: str) -> Optional[Dict[str, Any]]:
        """提取插入数据（简化版本，实际可能需要更复杂的解析）"""
        # 这里实现一个简化的插入数据提取
        # 实际项目中可能需要更复杂的SQL解析
        insert_match = re.search(r"INSERT INTO.*?VALUES\s*\((.*?)\)", log_content, re.IGNORECASE | re.DOTALL)
        if insert_match:
            values_str = insert_match.group(1)
            # 简单的值解析（实际需要更健壮的实现）
            try:
                # 移除引号并分割值
                values = [v.strip().strip("'").strip('"') for v in values_str.split(',')]
                return {"values": values}
            except:
                return {"raw_values": values_str}
        return None
    
    def _extract_error_message(self, log_content: str) -> str:
        """提取错误信息"""
        # 提取ERROR行或关键错误信息
        error_lines = []
        for line in log_content.split('\n'):
            if 'ERROR' in line.upper() or 'FATAL' in line.upper():
                error_lines.append(line.strip())
        
        if error_lines:
            return '; '.join(error_lines)
        return log_content.strip()
    
    def _extract_sql_statement(self, log_content: str) -> Optional[str]:
        """提取原始SQL语句"""
        for pattern in self.SQL_PATTERNS:
            if "STATEMENT:" in pattern or "QUERY:" in pattern:
                match = re.search(pattern, log_content, re.IGNORECASE | re.MULTILINE)
                if match:
                    return match.group(1).strip()
        return None
    
    def _calculate_confidence(self, error_type: ErrorType, table_name: Optional[str], error_message: str) -> float:
        """计算识别置信度"""
        confidence = 0.0
        
        # 基础置信度
        if error_type != ErrorType.UNKNOWN:
            confidence += 0.4
        
        # 表名提取成功
        if table_name:
            confidence += 0.3
        
        # 错误信息完整性
        if len(error_message) > 10:
            confidence += 0.2
        
        # 特定关键词存在
        if any(keyword in error_message.lower() for keyword in ['value too long', 'character varying', 'varchar']):
            confidence += 0.1
        
        return min(confidence, 1.0)

# ==================== 数据库配置 ====================

# 硬编码的数据库配置
DATABASE_CONFIG = {
    "host": "127.0.0.1",
    "port": 5432,
    "user": "usr_ai", 
    "password": "123456",
    "database": "postgres",
    "max_size": 20,  # 连接池最大连接数
    "min_size": 5,   # 连接池最小连接数
    "command_timeout": 300,  # 命令超时时间（秒）
}

# ==================== 数据库操作类 ====================

class DatabaseManager:
    """数据库管理器，使用连接池管理数据库连接"""
    
    def __init__(self):
        self.pool = None
    
    async def init_pool(self):
        """初始化数据库连接池"""
        if self.pool is None:
            try:
                self.pool = await asyncpg.create_pool(**DATABASE_CONFIG)
                logger.info("数据库连接池初始化成功")
            except Exception as e:
                logger.error(f"数据库连接池初始化失败: {e}")
                raise
    
    async def close_pool(self):
        """关闭数据库连接池"""
        if self.pool:
            await self.pool.close()
            logger.info("数据库连接池已关闭")
    
    async def get_table_schema(self, table_name: str, schema_name: str = "public") -> TableSchemaOutput:
        """获取表结构信息"""
        try:
            await self.init_pool()
            
            async with self.pool.acquire() as conn:
                # 检查表是否存在
                table_exists_query = """
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = $1 AND table_name = $2
                    );
                """
                table_exists = await conn.fetchval(table_exists_query, schema_name, table_name)
                
                if not table_exists:
                    return TableSchemaOutput(
                        table_name=table_name,
                        schema_name=schema_name,
                        fields=[],
                        total_fields=0,
                        table_exists=False
                    )
                
                # 获取字段信息
                schema_query = """
                    SELECT 
                        column_name,
                        data_type,
                        character_maximum_length,
                        numeric_precision,
                        numeric_scale,
                        is_nullable,
                        column_default,
                        ordinal_position
                    FROM information_schema.columns
                    WHERE table_schema = $1 AND table_name = $2
                    ORDER BY ordinal_position;
                """
                
                rows = await conn.fetch(schema_query, schema_name, table_name)
            
            fields = []
            for row in rows:
                field = FieldInfo(
                    column_name=row['column_name'],
                    data_type=row['data_type'],
                    character_maximum_length=row['character_maximum_length'],
                    numeric_precision=row['numeric_precision'],
                    numeric_scale=row['numeric_scale'],
                    is_nullable=row['is_nullable'] == 'YES',
                    column_default=row['column_default'],
                    ordinal_position=row['ordinal_position']
                )
                fields.append(field)
            
            return TableSchemaOutput(
                table_name=table_name,
                schema_name=schema_name,
                fields=fields,
                total_fields=len(fields),
                table_exists=True
            )
            
        except Exception as e:
            logger.error(f"获取表结构失败: {e}")
            raise Exception(f"获取表结构失败: {str(e)}")
    
    async def modify_table_schema(self, table_name: str, schema_name: str, 
                                modifications: List[ModificationOperation], dry_run: bool = True) -> TableModificationOutput:
        """修改表结构"""
        sql_statements = []
        
        # 生成SQL语句
        for mod in modifications:
            if mod.operation_type == "ALTER_LENGTH":
                sql = f"ALTER TABLE {schema_name}.{table_name} ALTER COLUMN {mod.field_name} TYPE {mod.new_definition};"
                sql_statements.append(sql)
            elif mod.operation_type == "ALTER_TYPE":
                sql = f"ALTER TABLE {schema_name}.{table_name} ALTER COLUMN {mod.field_name} TYPE {mod.new_definition};"
                sql_statements.append(sql)
        
        if dry_run:
            return TableModificationOutput(
                sql_statements=sql_statements,
                execution_status="DRY_RUN",
                affected_rows=0,
                success=True,
                error_message=None,
                is_dry_run=True
            )
        
        # 实际执行SQL
        try:
            await self.init_pool()
            
            async with self.pool.acquire() as conn:
                affected_rows = 0
                
                for sql in sql_statements:
                    result = await conn.execute(sql)
                    logger.info(f"执行SQL: {sql}, 结果: {result}")
                    affected_rows += 1
            
            return TableModificationOutput(
                sql_statements=sql_statements,
                execution_status="SUCCESS",
                affected_rows=affected_rows,
                success=True,
                error_message=None,
                is_dry_run=False
            )
            
        except Exception as e:
            logger.error(f"修改表结构失败: {e}")
            return TableModificationOutput(
                sql_statements=sql_statements,
                execution_status="ERROR",
                affected_rows=0,
                success=False,
                error_message=str(e),
                is_dry_run=False
            )

# ==================== 字段诊断器 ====================

class FieldDiagnosisEngine:
    """字段问题诊断引擎"""
    
    def diagnose_field_problems(self, table_schema: TableSchemaOutput, 
                              insert_data: Dict[str, Any], error_type: ErrorType) -> FieldDiagnosisOutput:
        """诊断字段问题"""
        problem_fields = []
        
        if error_type in [ErrorType.VARCHAR_LENGTH, ErrorType.CHAR_LENGTH]:
            problem_fields = self._diagnose_length_problems(table_schema, insert_data)
        elif error_type == ErrorType.NUMERIC_PRECISION:
            problem_fields = self._diagnose_numeric_problems(table_schema, insert_data)
        
        modification_needed = len(problem_fields) > 0
        diagnosis_summary = self._generate_diagnosis_summary(problem_fields, error_type)
        
        return FieldDiagnosisOutput(
            problem_fields=problem_fields,
            diagnosis_summary=diagnosis_summary,
            modification_needed=modification_needed
        )
    
    def _diagnose_length_problems(self, table_schema: TableSchemaOutput, insert_data: Dict[str, Any]) -> List[FieldProblem]:
        """诊断长度问题"""
        problems = []
        
        # 如果有具体的值数据
        if "values" in insert_data:
            values = insert_data["values"]
            for i, value in enumerate(values):
                if i < len(table_schema.fields):
                    field = table_schema.fields[i]
                    if field.data_type in ['character varying', 'varchar', 'character', 'char']:
                        if field.character_maximum_length and len(str(value)) > field.character_maximum_length:
                            suggested_length = max(len(str(value)) * 2, field.character_maximum_length * 2)
                            problem = FieldProblem(
                                field_name=field.column_name,
                                current_length=field.character_maximum_length,
                                required_length=len(str(value)),
                                data_value=value,
                                suggested_length=suggested_length
                            )
                            problems.append(problem)
        
        return problems
    
    def _diagnose_numeric_problems(self, table_schema: TableSchemaOutput, insert_data: Dict[str, Any]) -> List[FieldProblem]:
        """诊断数值问题"""
        problems = []
        # 这里可以实现数值精度问题的诊断逻辑
        return problems
    
    def _generate_diagnosis_summary(self, problems: List[FieldProblem], error_type: ErrorType) -> str:
        """生成诊断摘要"""
        if not problems:
            return "未发现具体的字段问题，可能需要手动检查数据和表结构。"
        
        summary_parts = []
        summary_parts.append(f"检测到 {len(problems)} 个字段存在问题:")
        
        for problem in problems:
            if error_type in [ErrorType.VARCHAR_LENGTH, ErrorType.CHAR_LENGTH]:
                summary_parts.append(
                    f"- 字段 '{problem.field_name}': 当前限制 {problem.current_length} 字符，"
                    f"需要 {problem.required_length} 字符，建议调整为 {problem.suggested_length} 字符"
                )
        
        return "\n".join(summary_parts)

# ==================== MCP Server 定义 ====================

# 创建FastMCP实例
mcp = FastMCP(name="PostgreSQL Log Analysis Server")

# 初始化组件
log_analyzer = PostgreSQLLogAnalyzer()
db_manager = DatabaseManager()
diagnosis_engine = FieldDiagnosisEngine()

# 服务器启动时自动初始化数据库连接池
async def initialize_db_on_startup():
    """服务器启动时初始化数据库连接池"""
    try:
        await db_manager.init_pool()
        logger.info("数据库连接池初始化成功")
    except Exception as e:
        logger.error(f"数据库连接池初始化失败: {e}")

# 在第一次工具调用时初始化（懒加载方式）
_db_initialized = False

async def ensure_db_initialized():
    """确保数据库连接池已初始化"""
    global _db_initialized
    if not _db_initialized:
        await db_manager.init_pool()
        _db_initialized = True

@mcp.tool
def analyze_postgresql_log(log_content: str, log_type: str = "error") -> LogAnalysisOutput:
    """
    分析PostgreSQL日志，识别错误类型和提取相关信息
    
    Args:
        log_content: PostgreSQL日志内容
        log_type: 日志类型（error, warning, info等）
    
    Returns:
        LogAnalysisOutput: 分析结果，包含错误类型、表名、插入数据等信息
    """
    try:
        input_data = LogAnalysisInput(log_content=log_content, log_type=log_type)
        result = log_analyzer.analyze_log(input_data.log_content)
        logger.info(f"日志分析完成，错误类型: {result.error_type}, 表名: {result.table_name}")
        return result
    except Exception as e:
        logger.error(f"日志分析失败: {e}")
        return LogAnalysisOutput(
            error_type=ErrorType.UNKNOWN,
            table_name=None,
            insert_data=None,
            error_message=f"分析失败: {str(e)}",
            confidence=0.0
        )

@mcp.tool
async def get_table_schema(table_name: str, schema_name: str = "public") -> TableSchemaOutput:
    """
    获取数据库表的结构信息
    
    Args:
        table_name: 表名
        schema_name: schema名称，默认为public
    
    Returns:
        TableSchemaOutput: 表结构信息，包含所有字段的详细信息
    """
    try:
        await ensure_db_initialized()  # 确保数据库连接池已初始化
        result = await db_manager.get_table_schema(table_name, schema_name)
        logger.info(f"获取表结构完成，表: {table_name}, 字段数: {result.total_fields}")
        return result
    except Exception as e:
        logger.error(f"获取表结构失败: {e}")
        raise Exception(f"获取表结构失败: {str(e)}")

@mcp.tool
def diagnose_field_problems(table_schema: Dict[str, Any], insert_data: Dict[str, Any], 
                           error_type: str) -> FieldDiagnosisOutput:
    """
    诊断表字段问题，分析导致错误的具体字段
    
    Args:
        table_schema: 表结构信息（TableSchemaOutput的字典形式）
        insert_data: 插入数据
        error_type: 错误类型
    
    Returns:
        FieldDiagnosisOutput: 诊断结果，包含问题字段和修改建议
    """
    try:
        # 转换输入数据
        schema_obj = TableSchemaOutput(**table_schema)
        error_type_enum = ErrorType(error_type)
        
        result = diagnosis_engine.diagnose_field_problems(schema_obj, insert_data, error_type_enum)
        logger.info(f"字段诊断完成，发现 {len(result.problem_fields)} 个问题字段")
        return result
    except Exception as e:
        logger.error(f"字段诊断失败: {e}")
        return FieldDiagnosisOutput(
            problem_fields=[],
            diagnosis_summary=f"诊断失败: {str(e)}",
            modification_needed=False
        )

@mcp.tool
async def modify_table_schema(table_name: str, modifications: List[Dict[str, Any]], 
                             schema_name: str = "public", dry_run: bool = True) -> TableModificationOutput:
    """
    修改数据库表结构
    
    Args:
        table_name: 表名
        modifications: 修改操作列表，每个操作包含field_name, operation_type, new_definition
        schema_name: schema名称，默认为public
        dry_run: 是否为预览模式，默认为True
    
    Returns:
        TableModificationOutput: 修改结果，包含SQL语句和执行状态
    """
    try:
        await ensure_db_initialized()  # 确保数据库连接池已初始化
        
        # 转换修改操作
        mod_objects = [ModificationOperation(**mod) for mod in modifications]
        
        result = await db_manager.modify_table_schema(
            table_name, schema_name, mod_objects, dry_run
        )
        
        logger.info(f"表结构修改完成，表: {table_name}, 操作数: {len(modifications)}, dry_run: {dry_run}")
        return result
    except Exception as e:
        logger.error(f"表结构修改失败: {e}")
        return TableModificationOutput(
            sql_statements=[],
            execution_status="ERROR",
            affected_rows=0,
            success=False,
            error_message=str(e),
            is_dry_run=dry_run
        )

# ==================== 主程序入口 ====================

if __name__ == "__main__":
    # 运行MCP服务器
    print("启动PostgreSQL日志分析MCP Server (Streamable HTTP)...")
    print(f"数据库连接: {DATABASE_CONFIG['host']}:{DATABASE_CONFIG['port']}/{DATABASE_CONFIG['database']}")
    
    # 可以通过环境变量配置端口和主机
    import os
    port = int(os.getenv("MCP_PORT", 8005))
    host = os.getenv("MCP_HOST", "0.0.0.0")
    
    print(f"服务器将运行在 http://{host}:{port}")
    print(f"MCP端点 (Streamable HTTP): http://{host}:{port}/mcp/")
    print("协议: Streamable HTTP (推荐的生产环境transport)")
    print()
    print("注意: 数据库连接池将在第一次工具调用时自动初始化")
    print("使用 Ctrl+C 停止服务器")
    print()
    
    try:
        # 使用streamable-http transport
        mcp.run(
            transport="streamable-http",  # 明确指定streamable-http
            host=host,
            port=port,
            show_banner=True  # 显示FastMCP启动横幅
        )
    except KeyboardInterrupt:
        print("\n正在关闭服务器...")
        # 清理数据库连接池
        import asyncio
        try:
            asyncio.run(db_manager.close_pool())
            print("数据库连接池已关闭")
        except:
            pass
        print("服务器已停止")
    except Exception as e:
        print(f"服务器启动失败: {e}")
        import asyncio
        try:
            asyncio.run(db_manager.close_pool())
        except:
            pass