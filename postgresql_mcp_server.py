"""
PostgreSQL日志分析和表结构修改MCP Server
基于FastMCP开发，提供4个核心工具用于分析PostgreSQL错误日志并自动修复表结构问题

主要功能：
1. analyze_postgresql_log - 分析PostgreSQL错误日志，识别错误类型和提取相关信息
2. get_table_schema - 获取数据库表的结构信息
3. diagnose_field_problems - 诊断表字段问题，分析导致错误的具体字段
4. modify_table_schema - 修改数据库表结构（支持预览和实际执行）

优化特性：
- 使用Pydantic模型进行严格的输入输出验证
- 支持结构化输出和输出模式
- 异步数据库连接池管理
- 详细的工具描述和参数说明
- 错误处理和日志记录
"""

import re
import asyncio
import logging
from typing import Dict, List, Optional, Any, Annotated
from enum import Enum

import asyncpg
from pydantic import BaseModel, Field
from fastmcp import FastMCP

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# ==================== 数据模型定义 ====================

class ErrorType(str, Enum):
    """PostgreSQL错误类型枚举

    支持的错误类型：
    - VARCHAR_LENGTH: VARCHAR字段长度超限
    - CHAR_LENGTH: CHAR字段长度超限
    - TEXT_LENGTH: TEXT字段长度超限
    - NUMERIC_PRECISION: 数值精度溢出
    - CONSTRAINT_VIOLATION: 约束违反
    - UNKNOWN: 未知错误类型
    """
    VARCHAR_LENGTH = "varchar_length"
    CHAR_LENGTH = "char_length"
    TEXT_LENGTH = "text_length"
    NUMERIC_PRECISION = "numeric_precision"
    CONSTRAINT_VIOLATION = "constraint_violation"
    UNKNOWN = "unknown"

class LogAnalysisOutput(BaseModel):
    """PostgreSQL日志分析结果

    包含错误类型识别、表名提取、数据解析等完整分析信息
    """
    error_type: ErrorType = Field(
        ...,
        description="识别的PostgreSQL错误类型，用于后续诊断和修复"
    )
    table_name: Optional[str] = Field(
        None,
        description="涉及的数据库表名，从SQL语句中提取"
    )
    insert_data: Optional[Dict[str, Any]] = Field(
        None,
        description="尝试插入的数据内容，用于字段问题诊断"
    )
    error_message: str = Field(
        ...,
        description="完整的错误信息，包含所有相关错误详情"
    )
    confidence: Annotated[float, Field(
        ...,
        ge=0.0,
        le=1.0,
        description="错误类型识别的置信度分数，范围0-1，越高表示识别越准确"
    )]
    original_sql: Optional[str] = Field(
        None,
        description="从日志中提取的原始SQL语句"
    )

class FieldInfo(BaseModel):
    """数据库字段详细信息

    包含字段的完整元数据，用于表结构分析和问题诊断
    """
    column_name: str = Field(..., description="字段名称")
    data_type: str = Field(..., description="PostgreSQL数据类型（如varchar, integer, text等）")
    character_maximum_length: Optional[int] = Field(
        None,
        description="字符类型字段的最大长度限制，仅适用于varchar、char等类型"
    )
    numeric_precision: Optional[int] = Field(
        None,
        description="数值类型字段的精度（总位数），仅适用于numeric、decimal等类型"
    )
    numeric_scale: Optional[int] = Field(
        None,
        description="数值类型字段的标度（小数位数），仅适用于numeric、decimal等类型"
    )
    is_nullable: bool = Field(..., description="字段是否允许NULL值")
    column_default: Optional[str] = Field(None, description="字段的默认值表达式")
    ordinal_position: Annotated[int, Field(
        ...,
        ge=1,
        description="字段在表中的位置序号，从1开始"
    )]

class TableSchemaOutput(BaseModel):
    """数据库表结构完整信息

    包含表的所有字段信息和元数据，用于结构分析和修改决策
    """
    table_name: str = Field(..., description="数据库表名")
    schema_name: str = Field(..., description="数据库模式名称，通常为'public'")
    fields: List[FieldInfo] = Field(..., description="表中所有字段的详细信息列表")
    total_fields: Annotated[int, Field(
        ...,
        ge=0,
        description="表中字段的总数量"
    )]
    table_exists: bool = Field(..., description="表是否在数据库中存在")

class FieldProblem(BaseModel):
    """字段问题详细信息

    描述导致错误的具体字段问题和建议的解决方案
    """
    field_name: str = Field(..., description="存在问题的字段名称")
    current_length: Optional[int] = Field(
        None,
        description="字段当前的长度限制，仅适用于字符类型字段"
    )
    required_length: Annotated[int, Field(
        ...,
        ge=1,
        description="数据实际需要的最小长度"
    )]
    data_value: Any = Field(..., description="导致问题的具体数据值")
    suggested_length: Annotated[int, Field(
        ...,
        ge=1,
        description="建议设置的新字段长度，通常为required_length的1.5-2倍以预留空间"
    )]

class FieldDiagnosisOutput(BaseModel):
    """字段问题诊断结果

    包含所有问题字段的详细分析和修改建议
    """
    problem_fields: List[FieldProblem] = Field(
        ...,
        description="检测到的所有问题字段列表，每个字段包含详细的问题分析"
    )
    diagnosis_summary: str = Field(
        ...,
        description="诊断结果的文字摘要，包含问题数量和主要建议"
    )
    modification_needed: bool = Field(
        ...,
        description="是否需要修改表结构来解决检测到的问题"
    )

class ModificationOperation(BaseModel):
    """数据库表结构修改操作

    定义单个字段的修改操作，包含操作类型和新的字段定义
    """
    field_name: str = Field(..., description="要修改的字段名称")
    operation_type: str = Field(
        ...,
        description="修改操作类型，支持：ALTER_LENGTH（修改长度）、ALTER_TYPE（修改类型）等"
    )
    new_definition: str = Field(
        ...,
        description="新的字段定义，如'VARCHAR(500)'、'TEXT'等完整的PostgreSQL类型定义"
    )

class TableModificationOutput(BaseModel):
    """表结构修改执行结果

    包含生成的SQL语句、执行状态和详细的结果信息
    """
    sql_statements: List[str] = Field(
        ...,
        description="生成的所有SQL ALTER TABLE语句列表"
    )
    execution_status: str = Field(
        ...,
        description="执行状态：DRY_RUN（预览模式）、SUCCESS（成功）、ERROR（失败）"
    )
    affected_rows: Annotated[int, Field(
        default=0,
        ge=0,
        description="受影响的行数，通常为执行的SQL语句数量"
    )]
    success: bool = Field(..., description="操作是否成功完成")
    error_message: Optional[str] = Field(None, description="如果失败，包含详细的错误信息")
    is_dry_run: bool = Field(..., description="是否为预览模式，true表示未实际执行SQL")

# ==================== PostgreSQL日志分析器 ====================

class PostgreSQLLogAnalyzer:
    """PostgreSQL日志分析器"""
    
    # 常见错误模式
    ERROR_PATTERNS = {
        ErrorType.VARCHAR_LENGTH: [
            r"value too long for type character varying\((\d+)\)",
            r"value too long for type varchar\((\d+)\)",
        ],
        ErrorType.CHAR_LENGTH: [
            r"value too long for type character\((\d+)\)",
            r"value too long for type char\((\d+)\)",
        ],
        ErrorType.TEXT_LENGTH: [
            r"value too long for type text",
        ],
        ErrorType.NUMERIC_PRECISION: [
            r"numeric field overflow",
            r"value out of range for type",
        ]
    }
    
    # SQL语句提取模式
    SQL_PATTERNS = [
        r"INSERT INTO\s+([^\s\(]+)",
        r"UPDATE\s+([^\s]+)",
        r"STATEMENT:\s*(INSERT INTO.*?)(?:\n|$)",
        r"QUERY:\s*(INSERT INTO.*?)(?:\n|$)",
    ]
    
    def analyze_log(self, log_content: str) -> LogAnalysisOutput:
        """分析PostgreSQL日志"""
        error_type = self._identify_error_type(log_content)
        table_name = self._extract_table_name(log_content)
        insert_data = self._extract_insert_data(log_content)
        error_message = self._extract_error_message(log_content)
        original_sql = self._extract_sql_statement(log_content)
        confidence = self._calculate_confidence(error_type, table_name, error_message)
        
        return LogAnalysisOutput(
            error_type=error_type,
            table_name=table_name,
            insert_data=insert_data,
            error_message=error_message,
            confidence=confidence,
            original_sql=original_sql
        )
    
    def _identify_error_type(self, log_content: str) -> ErrorType:
        """识别错误类型"""
        for error_type, patterns in self.ERROR_PATTERNS.items():
            for pattern in patterns:
                if re.search(pattern, log_content, re.IGNORECASE):
                    return error_type
        return ErrorType.UNKNOWN
    
    def _extract_table_name(self, log_content: str) -> Optional[str]:
        """提取表名"""
        for pattern in self.SQL_PATTERNS:
            match = re.search(pattern, log_content, re.IGNORECASE | re.MULTILINE)
            if match:
                if "INSERT INTO" in pattern:
                    return match.group(1).strip('"').strip("'")
                else:
                    return match.group(1).strip('"').strip("'")
        return None
    
    def _extract_insert_data(self, log_content: str) -> Optional[Dict[str, Any]]:
        """提取插入数据（简化版本，实际可能需要更复杂的解析）"""
        # 这里实现一个简化的插入数据提取
        # 实际项目中可能需要更复杂的SQL解析
        insert_match = re.search(r"INSERT INTO.*?VALUES\s*\((.*?)\)", log_content, re.IGNORECASE | re.DOTALL)
        if insert_match:
            values_str = insert_match.group(1)
            # 简单的值解析（实际需要更健壮的实现）
            try:
                # 移除引号并分割值
                values = [v.strip().strip("'").strip('"') for v in values_str.split(',')]
                return {"values": values}
            except:
                return {"raw_values": values_str}
        return None
    
    def _extract_error_message(self, log_content: str) -> str:
        """提取错误信息"""
        # 提取ERROR行或关键错误信息
        error_lines = []
        for line in log_content.split('\n'):
            if 'ERROR' in line.upper() or 'FATAL' in line.upper():
                error_lines.append(line.strip())
        
        if error_lines:
            return '; '.join(error_lines)
        return log_content.strip()
    
    def _extract_sql_statement(self, log_content: str) -> Optional[str]:
        """提取原始SQL语句"""
        for pattern in self.SQL_PATTERNS:
            if "STATEMENT:" in pattern or "QUERY:" in pattern:
                match = re.search(pattern, log_content, re.IGNORECASE | re.MULTILINE)
                if match:
                    return match.group(1).strip()
        return None
    
    def _calculate_confidence(self, error_type: ErrorType, table_name: Optional[str], error_message: str) -> float:
        """计算识别置信度"""
        confidence = 0.0
        
        # 基础置信度
        if error_type != ErrorType.UNKNOWN:
            confidence += 0.4
        
        # 表名提取成功
        if table_name:
            confidence += 0.3
        
        # 错误信息完整性
        if len(error_message) > 10:
            confidence += 0.2
        
        # 特定关键词存在
        if any(keyword in error_message.lower() for keyword in ['value too long', 'character varying', 'varchar']):
            confidence += 0.1
        
        return min(confidence, 1.0)

# ==================== 数据库配置 ====================

# 硬编码的数据库配置
DATABASE_CONFIG = {
    "host": "127.0.0.1",
    "port": 5432,
    "user": "usr_ai", 
    "password": "123456",
    "database": "postgres",
    "max_size": 20,  # 连接池最大连接数
    "min_size": 5,   # 连接池最小连接数
    "command_timeout": 300,  # 命令超时时间（秒）
}

# ==================== 数据库操作类 ====================

class DatabaseManager:
    """数据库管理器，使用连接池管理数据库连接"""
    
    def __init__(self):
        self.pool = None
    
    async def init_pool(self):
        """初始化数据库连接池"""
        if self.pool is None:
            try:
                self.pool = await asyncpg.create_pool(**DATABASE_CONFIG)
                logger.info("数据库连接池初始化成功")
            except Exception as e:
                logger.error(f"数据库连接池初始化失败: {e}")
                raise
    
    async def close_pool(self):
        """关闭数据库连接池"""
        if self.pool:
            await self.pool.close()
            logger.info("数据库连接池已关闭")
    
    async def get_table_schema(self, table_name: str, schema_name: str = "public") -> TableSchemaOutput:
        """获取表结构信息"""
        try:
            await self.init_pool()
            
            async with self.pool.acquire() as conn:
                # 检查表是否存在
                table_exists_query = """
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = $1 AND table_name = $2
                    );
                """
                table_exists = await conn.fetchval(table_exists_query, schema_name, table_name)
                
                if not table_exists:
                    return TableSchemaOutput(
                        table_name=table_name,
                        schema_name=schema_name,
                        fields=[],
                        total_fields=0,
                        table_exists=False
                    )
                
                # 获取字段信息
                schema_query = """
                    SELECT 
                        column_name,
                        data_type,
                        character_maximum_length,
                        numeric_precision,
                        numeric_scale,
                        is_nullable,
                        column_default,
                        ordinal_position
                    FROM information_schema.columns
                    WHERE table_schema = $1 AND table_name = $2
                    ORDER BY ordinal_position;
                """
                
                rows = await conn.fetch(schema_query, schema_name, table_name)
            
            fields = []
            for row in rows:
                field = FieldInfo(
                    column_name=row['column_name'],
                    data_type=row['data_type'],
                    character_maximum_length=row['character_maximum_length'],
                    numeric_precision=row['numeric_precision'],
                    numeric_scale=row['numeric_scale'],
                    is_nullable=row['is_nullable'] == 'YES',
                    column_default=row['column_default'],
                    ordinal_position=row['ordinal_position']
                )
                fields.append(field)
            
            return TableSchemaOutput(
                table_name=table_name,
                schema_name=schema_name,
                fields=fields,
                total_fields=len(fields),
                table_exists=True
            )
            
        except Exception as e:
            logger.error(f"获取表结构失败: {e}")
            raise Exception(f"获取表结构失败: {str(e)}")
    
    async def modify_table_schema(self, table_name: str, schema_name: str, 
                                modifications: List[ModificationOperation], dry_run: bool = True) -> TableModificationOutput:
        """修改表结构"""
        sql_statements = []
        
        # 生成SQL语句
        for mod in modifications:
            if mod.operation_type == "ALTER_LENGTH":
                sql = f"ALTER TABLE {schema_name}.{table_name} ALTER COLUMN {mod.field_name} TYPE {mod.new_definition};"
                sql_statements.append(sql)
            elif mod.operation_type == "ALTER_TYPE":
                sql = f"ALTER TABLE {schema_name}.{table_name} ALTER COLUMN {mod.field_name} TYPE {mod.new_definition};"
                sql_statements.append(sql)
        
        if dry_run:
            return TableModificationOutput(
                sql_statements=sql_statements,
                execution_status="DRY_RUN",
                affected_rows=0,
                success=True,
                error_message=None,
                is_dry_run=True
            )
        
        # 实际执行SQL
        try:
            await self.init_pool()
            
            async with self.pool.acquire() as conn:
                affected_rows = 0
                
                for sql in sql_statements:
                    result = await conn.execute(sql)
                    logger.info(f"执行SQL: {sql}, 结果: {result}")
                    affected_rows += 1
            
            return TableModificationOutput(
                sql_statements=sql_statements,
                execution_status="SUCCESS",
                affected_rows=affected_rows,
                success=True,
                error_message=None,
                is_dry_run=False
            )
            
        except Exception as e:
            logger.error(f"修改表结构失败: {e}")
            return TableModificationOutput(
                sql_statements=sql_statements,
                execution_status="ERROR",
                affected_rows=0,
                success=False,
                error_message=str(e),
                is_dry_run=False
            )

# ==================== 字段诊断器 ====================

class FieldDiagnosisEngine:
    """字段问题诊断引擎"""
    
    def diagnose_field_problems(self, table_schema: TableSchemaOutput, 
                              insert_data: Dict[str, Any], error_type: ErrorType) -> FieldDiagnosisOutput:
        """诊断字段问题"""
        problem_fields = []
        
        if error_type in [ErrorType.VARCHAR_LENGTH, ErrorType.CHAR_LENGTH]:
            problem_fields = self._diagnose_length_problems(table_schema, insert_data)
        elif error_type == ErrorType.NUMERIC_PRECISION:
            problem_fields = self._diagnose_numeric_problems(table_schema, insert_data)
        
        modification_needed = len(problem_fields) > 0
        diagnosis_summary = self._generate_diagnosis_summary(problem_fields, error_type)
        
        return FieldDiagnosisOutput(
            problem_fields=problem_fields,
            diagnosis_summary=diagnosis_summary,
            modification_needed=modification_needed
        )
    
    def _diagnose_length_problems(self, table_schema: TableSchemaOutput, insert_data: Dict[str, Any]) -> List[FieldProblem]:
        """诊断长度问题"""
        problems = []
        
        # 如果有具体的值数据
        if "values" in insert_data:
            values = insert_data["values"]
            for i, value in enumerate(values):
                if i < len(table_schema.fields):
                    field = table_schema.fields[i]
                    if field.data_type in ['character varying', 'varchar', 'character', 'char']:
                        if field.character_maximum_length and len(str(value)) > field.character_maximum_length:
                            suggested_length = max(len(str(value)) * 2, field.character_maximum_length * 2)
                            problem = FieldProblem(
                                field_name=field.column_name,
                                current_length=field.character_maximum_length,
                                required_length=len(str(value)),
                                data_value=value,
                                suggested_length=suggested_length
                            )
                            problems.append(problem)
        
        return problems
    
    def _diagnose_numeric_problems(self, table_schema: TableSchemaOutput, insert_data: Dict[str, Any]) -> List[FieldProblem]:
        """诊断数值问题"""
        problems = []
        # 这里可以实现数值精度问题的诊断逻辑
        return problems
    
    def _generate_diagnosis_summary(self, problems: List[FieldProblem], error_type: ErrorType) -> str:
        """生成诊断摘要"""
        if not problems:
            return "未发现具体的字段问题，可能需要手动检查数据和表结构。"
        
        summary_parts = []
        summary_parts.append(f"检测到 {len(problems)} 个字段存在问题:")
        
        for problem in problems:
            if error_type in [ErrorType.VARCHAR_LENGTH, ErrorType.CHAR_LENGTH]:
                summary_parts.append(
                    f"- 字段 '{problem.field_name}': 当前限制 {problem.current_length} 字符，"
                    f"需要 {problem.required_length} 字符，建议调整为 {problem.suggested_length} 字符"
                )
        
        return "\n".join(summary_parts)

# ==================== MCP Server 定义 ====================

# 创建FastMCP实例 - PostgreSQL智能诊断和修复服务器
mcp = FastMCP(
    name="PostgreSQL Log Analysis & Schema Repair Server"
)

# 初始化组件
log_analyzer = PostgreSQLLogAnalyzer()
db_manager = DatabaseManager()
diagnosis_engine = FieldDiagnosisEngine()

# 服务器启动时自动初始化数据库连接池
async def initialize_db_on_startup():
    """服务器启动时初始化数据库连接池"""
    try:
        await db_manager.init_pool()
        logger.info("数据库连接池初始化成功")
    except Exception as e:
        logger.error(f"数据库连接池初始化失败: {e}")

# 在第一次工具调用时初始化（懒加载方式）
_db_initialized = False

async def ensure_db_initialized():
    """确保数据库连接池已初始化"""
    global _db_initialized
    if not _db_initialized:
        await db_manager.init_pool()
        _db_initialized = True

@mcp.tool
def analyze_postgresql_log(
    log_content: Annotated[str, Field(
        description="完整的PostgreSQL错误日志内容。必须包含完整的错误消息和相关上下文。"
                   "最佳格式：包含'ERROR:'开头的错误行和'STATEMENT:'开头的SQL语句行。"
                   "示例：'ERROR: value too long for type character varying(50)\\nSTATEMENT: INSERT INTO users...'。"
                   "日志越完整，分析结果越准确。",
        min_length=20,
        max_length=100000
    )],
    log_type: Annotated[str, Field(
        description="PostgreSQL日志级别，用于分类和过滤。"
                   "标准值：'error'（错误，默认）、'warning'（警告）、'info'（信息）、'debug'（调试）、'fatal'（致命错误）。"
                   "对于故障排查，通常使用'error'。",
        pattern="^(error|warning|info|debug|fatal)$"
    )] = "error"
) -> LogAnalysisOutput:
    """🔍 PostgreSQL错误日志智能分析器 - 数据库故障诊断的第一步工具

    **核心功能**：
    自动分析PostgreSQL错误日志，精确识别错误类型并提取关键信息，为后续修复提供准确依据。

    **🎯 错误类型识别能力**：
    • VARCHAR/CHAR字段长度超限 - 最常见的数据插入失败原因
    • TEXT字段长度问题 - 大文本数据处理错误
    • 数值精度溢出 - NUMERIC/DECIMAL字段精度不足
    • 约束违反 - 主键、外键、唯一约束等违反
    • 其他PostgreSQL常见错误类型

    **📊 提取的关键信息**：
    • 涉及的数据库表名（从SQL语句自动解析）
    • 完整的原始SQL语句（INSERT/UPDATE等）
    • 尝试插入的具体数据值（用于后续字段分析）
    • 错误识别置信度评分（0.0-1.0，越高越准确）

    **💡 最佳使用场景**：
    • 应用程序数据库操作失败时的首要诊断工具
    • 批量数据导入出错时的快速定位
    • 数据库字段设计不当导致的插入失败分析
    • 为自动化表结构修复提供准确的基础数据

    **⚠️ 重要提示**：
    • 提供完整日志可获得最佳分析效果（包含ERROR和STATEMENT行）
    • 置信度<0.5时建议人工复核，可能需要更多上下文
    • 支持多行日志文本，会自动提取最相关的错误信息
    """
    try:
        result = log_analyzer.analyze_log(log_content)
        logger.info(f"日志分析完成，错误类型: {result.error_type}, 表名: {result.table_name}")
        return result
    except Exception as e:
        logger.error(f"日志分析失败: {e}")
        return LogAnalysisOutput(
            error_type=ErrorType.UNKNOWN,
            table_name=None,
            insert_data=None,
            error_message=f"分析失败: {str(e)}",
            confidence=0.0,
            original_sql=None
        )

@mcp.tool
async def get_table_schema(
    table_name: Annotated[str, Field(
        description="要查询的PostgreSQL数据库表名。"
                   "支持标准表名格式，如：'users'、'order_items'、'product_catalog'等。"
                   "表名区分大小写，建议使用小写和下划线命名规范。"
                   "如果表不存在，工具会返回table_exists=false的结果。",
        min_length=1,
        max_length=63,  # PostgreSQL标识符最大长度
        pattern="^[a-zA-Z_][a-zA-Z0-9_]*$"
    )],
    schema_name: Annotated[str, Field(
        description="PostgreSQL数据库模式（schema）名称，用于指定表所在的命名空间。"
                   "默认值'public'适用于大多数标准PostgreSQL安装。"
                   "其他常见值：'information_schema'、'pg_catalog'或自定义模式名。"
                   "如果不确定，使用默认值'public'即可。",
        min_length=1,
        max_length=63,
        pattern="^[a-zA-Z_][a-zA-Z0-9_]*$"
    )] = "public"
) -> TableSchemaOutput:
    """📋 PostgreSQL表结构详细查询器 - 获取完整的表元数据信息

    **核心功能**：
    连接PostgreSQL数据库，查询指定表的完整结构信息，包括所有字段的详细元数据。

    **🔍 获取的详细信息**：
    • **字段基本信息**：字段名、数据类型、位置序号
    • **长度约束**：VARCHAR/CHAR字段的最大长度限制
    • **数值约束**：NUMERIC/DECIMAL字段的精度和标度
    • **空值约束**：字段是否允许NULL值
    • **默认值**：字段的默认值表达式
    • **表存在性**：验证表是否在数据库中存在

    **💡 主要使用场景**：
    • 在修改表结构前了解当前字段定义
    • 分析字段长度限制是否足够
    • 为字段问题诊断提供准确的结构基础
    • 验证表是否存在以避免后续操作错误
    • 了解字段的完整约束信息

    **🔗 工作流集成**：
    • 通常在analyze_postgresql_log之后使用
    • 为diagnose_field_problems提供必要的表结构数据
    • 在modify_table_schema之前验证当前结构

    **⚠️ 注意事项**：
    • 需要数据库连接权限和表的SELECT权限
    • 如果表不存在，返回table_exists=false而不是错误
    • 大表的字段数量可能较多，请注意结果数据量
    """
    try:
        await ensure_db_initialized()  # 确保数据库连接池已初始化
        result = await db_manager.get_table_schema(table_name, schema_name)
        logger.info(f"获取表结构完成，表: {table_name}, 字段数: {result.total_fields}")
        return result
    except Exception as e:
        logger.error(f"获取表结构失败: {e}")
        raise Exception(f"获取表结构失败: {str(e)}")

@mcp.tool
def diagnose_field_problems(
    table_schema: Annotated[Dict[str, Any], Field(
        description="完整的表结构信息，通常来自get_table_schema工具的返回结果。"
                   "必须包含字段：table_name、schema_name、fields（字段列表）、total_fields、table_exists。"
                   "每个字段对象需包含：column_name、data_type、character_maximum_length等完整元数据。"
                   "这是进行精确字段问题诊断的基础数据。"
    )],
    insert_data: Annotated[Dict[str, Any], Field(
        description="导致错误的插入数据内容，通常来自analyze_postgresql_log工具的extract结果。"
                   "格式示例：{'values': ['John Doe', '<EMAIL>', '这是一个很长的描述文本...']}。"
                   "或原始格式：{'raw_values': \"'John', '<EMAIL>', '长文本'\"}。"
                   "数据越完整，诊断结果越准确。"
    )],
    error_type: Annotated[str, Field(
        description="PostgreSQL错误类型，来自analyze_postgresql_log工具的识别结果。"
                   "支持的值：'varchar_length'（VARCHAR长度超限）、'char_length'（CHAR长度超限）、"
                   "'text_length'（TEXT长度问题）、'numeric_precision'（数值精度溢出）、"
                   "'constraint_violation'（约束违反）、'unknown'（未知错误）。"
                   "错误类型决定了诊断算法的选择。",
        pattern="^(varchar_length|char_length|text_length|numeric_precision|constraint_violation|unknown)$"
    )]
) -> FieldDiagnosisOutput:
    """🔬 字段问题精确诊断器 - 定位具体的问题字段和解决方案

    **核心功能**：
    结合表结构信息和错误数据，精确分析哪些字段存在问题，并提供具体的修复建议。

    **🎯 诊断能力**：
    • **长度问题诊断**：对比数据长度与字段限制，计算所需的最小长度
    • **精度问题分析**：分析数值数据与字段精度的匹配情况
    • **约束冲突检测**：识别违反的具体约束条件
    • **智能建议生成**：提供合理的字段长度扩展建议（通常为实际需求的1.5-2倍）

    **📊 输出详细信息**：
    • **问题字段列表**：每个问题字段的详细分析
    • **当前vs需求对比**：现有限制与实际需求的对比
    • **具体数据值**：导致问题的确切数据内容
    • **修改建议**：推荐的新字段定义
    • **诊断摘要**：问题总结和修复建议概述

    **💡 使用场景**：
    • 在获得日志分析和表结构信息后的深度诊断
    • 为表结构修改提供精确的参数依据
    • 批量数据问题的系统性分析
    • 数据库设计优化的决策支持

    **🔗 工作流位置**：
    1. analyze_postgresql_log（获取错误类型和数据）
    2. get_table_schema（获取表结构）
    3. **diagnose_field_problems**（当前工具 - 精确诊断）
    4. modify_table_schema（执行修复）

    **⚠️ 重要提示**：
    • 需要准确的表结构和插入数据才能进行精确诊断
    • 建议的字段长度包含安全余量，可根据实际需求调整
    • 对于复杂的约束问题，可能需要人工介入分析
    """
    try:
        # 转换输入数据
        schema_obj = TableSchemaOutput(**table_schema)
        error_type_enum = ErrorType(error_type)
        
        result = diagnosis_engine.diagnose_field_problems(schema_obj, insert_data, error_type_enum)
        logger.info(f"字段诊断完成，发现 {len(result.problem_fields)} 个问题字段")
        return result
    except Exception as e:
        logger.error(f"字段诊断失败: {e}")
        return FieldDiagnosisOutput(
            problem_fields=[],
            diagnosis_summary=f"诊断失败: {str(e)}",
            modification_needed=False
        )

@mcp.tool
async def modify_table_schema(
    table_name: Annotated[str, Field(
        description="要修改的PostgreSQL数据库表名。"
                   "必须是已存在的表，建议先用get_table_schema验证表的存在性。"
                   "支持标准表名格式，区分大小写。"
                   "示例：'users'、'product_catalog'、'order_items'等。",
        min_length=1,
        max_length=63,
        pattern="^[a-zA-Z_][a-zA-Z0-9_]*$"
    )],
    modifications: Annotated[List[Dict[str, Any]], Field(
        description="表结构修改操作列表，每个操作定义一个字段的修改。"
                   "每个操作必须包含：field_name（字段名）、operation_type（操作类型）、new_definition（新定义）。"
                   "operation_type支持：'ALTER_LENGTH'（修改长度）、'ALTER_TYPE'（修改类型）。"
                   "new_definition示例：'VARCHAR(500)'、'TEXT'、'NUMERIC(10,2)'等。"
                   "通常来自diagnose_field_problems工具的建议。",
        min_length=1,
        max_items=50
    )],
    schema_name: Annotated[str, Field(
        description="PostgreSQL数据库模式名称，指定表所在的命名空间。"
                   "默认'public'适用于标准安装。"
                   "必须与get_table_schema中使用的schema_name保持一致。",
        min_length=1,
        max_length=63,
        pattern="^[a-zA-Z_][a-zA-Z0-9_]*$"
    )] = "public",
    dry_run: Annotated[bool, Field(
        description="安全模式开关，控制是否实际执行SQL语句。"
                   "true（默认）：预览模式，只生成SQL语句不执行，用于验证修改计划。"
                   "false：执行模式，实际修改数据库表结构，需谨慎使用。"
                   "建议先用dry_run=true预览，确认无误后再设为false执行。"
    )] = True
) -> TableModificationOutput:
    """⚙️ PostgreSQL表结构修改执行器 - 安全地修改数据库表结构

    **核心功能**：
    根据诊断结果安全地修改PostgreSQL表结构，支持预览和实际执行两种模式。

    **🛠️ 支持的修改操作**：
    • **字段长度扩展**：VARCHAR/CHAR字段长度增加
    • **字段类型转换**：如VARCHAR转TEXT，NUMERIC精度调整
    • **批量字段修改**：一次性修改多个字段
    • **安全性验证**：修改前验证操作的合理性

    **🔒 安全特性**：
    • **预览模式**：默认dry_run=true，只生成SQL不执行
    • **SQL语句展示**：显示将要执行的完整ALTER TABLE语句
    • **回滚友好**：生成的SQL语句支持手动回滚
    • **错误处理**：详细的错误信息和执行状态反馈

    **📋 执行流程**：
    1. **验证阶段**：检查表存在性和修改操作合理性
    2. **SQL生成**：根据修改操作生成标准的ALTER TABLE语句
    3. **预览/执行**：根据dry_run参数决定是否实际执行
    4. **结果反馈**：返回详细的执行状态和结果信息

    **💡 最佳实践**：
    • **先预览后执行**：始终先用dry_run=true查看生成的SQL
    • **备份重要数据**：修改表结构前备份相关数据
    • **分批执行**：对于大表，考虑分批进行修改
    • **监控执行**：关注执行时间和系统资源使用

    **🔗 完整工作流**：
    1. analyze_postgresql_log（识别错误）
    2. get_table_schema（获取表结构）
    3. diagnose_field_problems（诊断问题）
    4. **modify_table_schema**（当前工具 - 执行修复）

    **⚠️ 重要警告**：
    • dry_run=false会实际修改数据库，操作不可逆
    • 大表的结构修改可能需要较长时间并锁定表
    • 建议在维护窗口期间执行实际修改操作
    • 确保有足够的数据库权限执行ALTER TABLE操作
    """
    try:
        await ensure_db_initialized()  # 确保数据库连接池已初始化
        
        # 转换修改操作
        mod_objects = [ModificationOperation(**mod) for mod in modifications]
        
        result = await db_manager.modify_table_schema(
            table_name, schema_name, mod_objects, dry_run
        )
        
        logger.info(f"表结构修改完成，表: {table_name}, 操作数: {len(modifications)}, dry_run: {dry_run}")
        return result
    except Exception as e:
        logger.error(f"表结构修改失败: {e}")
        return TableModificationOutput(
            sql_statements=[],
            execution_status="ERROR",
            affected_rows=0,
            success=False,
            error_message=str(e),
            is_dry_run=dry_run
        )

# ==================== 主程序入口 ====================

if __name__ == "__main__":
    # 运行MCP服务器
    print("🚀 启动PostgreSQL智能诊断和修复MCP服务器...")
    print("=" * 80)
    print("📋 服务器信息:")
    print(f"   名称: PostgreSQL Log Analysis & Schema Repair Server")
    print(f"   数据库: {DATABASE_CONFIG['host']}:{DATABASE_CONFIG['port']}/{DATABASE_CONFIG['database']}")
    print(f"   用户: {DATABASE_CONFIG['user']}")

    # 可以通过环境变量配置端口和主机
    import os
    port = int(os.getenv("MCP_PORT", 8005))
    host = os.getenv("MCP_HOST", "0.0.0.0")

    print(f"   服务地址: http://{host}:{port}")
    print(f"   MCP端点: http://{host}:{port}/mcp/")
    print(f"   传输协议: Streamable HTTP")
    print()
    print("🛠️  可用工具 (4个):")
    print("   1. analyze_postgresql_log    - 🔍 智能分析PostgreSQL错误日志")
    print("   2. get_table_schema         - 📋 获取数据库表结构详细信息")
    print("   3. diagnose_field_problems  - 🔬 精确诊断字段问题和解决方案")
    print("   4. modify_table_schema      - ⚙️  安全修改数据库表结构")
    print()
    print("🔄 完整工作流:")
    print("   日志分析 → 表结构查询 → 问题诊断 → 结构修复")
    print()
    print("⚠️  注意事项:")
    print("   • 数据库连接池将在首次工具调用时自动初始化")
    print("   • modify_table_schema默认为预览模式，需显式设置dry_run=false执行")
    print("   • 建议在维护窗口期间执行实际的表结构修改")
    print("   • 使用 Ctrl+C 停止服务器")
    print("=" * 80)
    
    try:
        # 使用streamable-http transport
        mcp.run(
            transport="streamable-http",  # 明确指定streamable-http
            host=host,
            port=port,
            show_banner=True  # 显示FastMCP启动横幅
        )
    except KeyboardInterrupt:
        print("\n正在关闭服务器...")
        # 清理数据库连接池
        import asyncio
        try:
            asyncio.run(db_manager.close_pool())
            print("数据库连接池已关闭")
        except:
            pass
        print("服务器已停止")
    except Exception as e:
        print(f"服务器启动失败: {e}")
        import asyncio
        try:
            asyncio.run(db_manager.close_pool())
        except:
            pass