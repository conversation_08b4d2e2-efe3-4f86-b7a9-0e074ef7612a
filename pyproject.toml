[project]
name = "fastmcp-demo"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "fastapi[standard]",
    "fastmcp",
    "psycopg2-binary",
    "asyncpg",
    "httpx",
    "python-multipart",
    "python-dotenv",
    "pygwalker>=********",
    "streamlit>=1.48.1",
]
[[tool.uv.index]]
url = "https://mirrors.aliyun.com/pypi/simple"
